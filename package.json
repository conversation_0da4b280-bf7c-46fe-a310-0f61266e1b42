{"name": "ux_assistant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^0.12.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/supabase-js": "^2.49.4", "@tldraw/ai": "^0.0.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dom-to-image": "^2.6.0", "dom-to-image-more": "^3.6.0", "dom-to-svg": "^0.12.2", "dom2svg": "^1.0.11", "embla-carousel-react": "^8.6.0", "groq-sdk": "^0.27.0", "html-to-image": "^1.11.13", "htmlsvg": "^1.2.3", "input-otp": "^1.4.2", "lodash.throttle": "^4.1.1", "lucide-react": "^0.503.0", "motion": "^12.10.1", "next": "15.3.1", "next-themes": "^0.4.6", "openai": "^5.10.1", "posthog-js": "^1.240.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-jsx-parser": "^2.4.0", "react-live": "^4.1.8", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "react-runner": "^1.0.5", "recharts": "^2.15.3", "sonner": "^2.0.3", "spectacle": "^10.2.0", "tailwind-merge": "^3.2.0", "tldraw": "^3.12.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.5", "@types/dom-to-image": "^2.6.7", "@types/lodash.throttle": "^4.1.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.8", "typescript": "^5"}}