export interface PromptHistoryItem {
  prompt: string;
  timestamp: number;
}

const STORAGE_KEY = 'prompt_history';
const MAX_HISTORY_ITEMS = 50;

export function usePromptHistory() {
  const addPromptToHistory = (prompt: string) => {
    try {
      const existingHistory = getPromptHistory();
      const newHistoryItem: PromptHistoryItem = {
        prompt,
        timestamp: Date.now(),
      };

      // Add new item to start of array and limit to MAX_HISTORY_ITEMS
      const updatedHistory = [newHistoryItem, ...existingHistory].slice(0, MAX_HISTORY_ITEMS);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error saving to prompt history:', error);
    }
  };

  const getPromptHistory = (): PromptHistoryItem[] => {
    try {
      const history = localStorage.getItem(STORAGE_KEY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error reading prompt history:', error);
      return [];
    }
  };

  return {
    addPromptToHistory,
    getPromptHistory,
  };
} 