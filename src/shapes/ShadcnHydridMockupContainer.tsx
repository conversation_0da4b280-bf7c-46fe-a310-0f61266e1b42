import { BaseBoxShape<PERSON>til, H<PERSON><PERSON>ontainer, TLBase<PERSON>ha<PERSON>, useEditor } from 'tldraw'
import { useEffect, useRef } from 'react'
import JsxParser from 'react-jsx-parser'
import React from 'react'
import * as LucideIcons from 'lucide-react'
import { LucideIcon } from 'lucide-react'

// Import Shadcn components the AI can use
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from "@/components/ui/select"
import { 
  Tabs, TabsContent, TabsList, TabsTrigger
} from "@/components/ui/tabs"
import { Toggle } from "@/components/ui/toggle"
import { 
  Accordion, AccordionContent, AccordionItem, AccordionTrigger 
} from "@/components/ui/accordion"
import { 
  Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarTrigger 
} from "@/components/ui/menubar"
import { Calendar } from "@/components/ui/calendar"
import { 
  Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList 
} from "@/components/ui/command"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { 
  Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import { 
  Carousel, CarouselContent, CarouselItem 
} from "@/components/ui/carousel"
import { 
  Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious 
} from "@/components/ui/pagination"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { 
  Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"

// Get Lucide icons while excluding any names that conflict with Shadcn
const lucideIconsWithoutConflicts = Object.entries(LucideIcons).reduce((acc, [name, Component]) => {
  // Exclude specific components that conflict with Shadcn
  const conflictingNames = ['Badge', 'Calendar', 'Command', 'Table']
  if (!conflictingNames.includes(name) && typeof Component === 'function' && name !== 'createLucideIcon') {
    acc[name] = Component
  }
  return acc
}, {} as Record<string, any>)

// Define the map of components available to JsxParser
const availableComponents = {
  Button,
  Input,
  Textarea,
  Label,
  Checkbox,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  Alert,
  AlertTitle,
  AlertDescription,
  Badge,
  Avatar,
  AvatarImage,
  AvatarFallback,
  Separator,
  Switch,
  Skeleton,
  Progress,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  Toggle,
  Accordion, AccordionItem, AccordionTrigger, AccordionContent,
  Menubar, MenubarMenu, MenubarTrigger, MenubarContent, MenubarItem, MenubarSeparator,
  Calendar,
  Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem,
  RadioGroup, RadioGroupItem,
  Slider,
  Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator,
  Carousel, CarouselContent, CarouselItem,
  Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious,
  ScrollArea, ScrollBar,
  Table, TableHeader, TableBody, TableFooter, TableRow, TableHead, TableCell, TableCaption,
  div: 'div' as React.ElementType,
  // Add missing HTML elements
  h1: 'h1' as React.ElementType,
  h2: 'h2' as React.ElementType,
  h3: 'h3' as React.ElementType,
  h4: 'h4' as React.ElementType,
  h5: 'h5' as React.ElementType,
  h6: 'h6' as React.ElementType,
  p: 'p' as React.ElementType,
  span: 'span' as React.ElementType,
  container: 'div' as React.ElementType, // container is typically a div with specific classes
  // Add support for images and SVG elements
  img: 'img' as React.ElementType,
  svg: 'svg' as React.ElementType,
  path: 'path' as React.ElementType,
  circle: 'circle' as React.ElementType,
  rect: 'rect' as React.ElementType,
  line: 'line' as React.ElementType,

  // Add Lucide icons
  ...lucideIconsWithoutConflicts,
}

export type ShadcnHydridMockupContainer = TLBaseShape<
  'ai-generated-ui', // Unique type name
  {
    jsxString: string // Prop to store the JSX string
    w: number
    h: number
  }
>

export class ShadcnHydridMockupContainerUtil extends BaseBoxShapeUtil<ShadcnHydridMockupContainer> {
  static type = 'shadcn-hydrid-mockup-container' as const
  static props = {
    jsxString: {
      type: 'string',
      default: '<Button>Default</Button>', // Default content
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 400,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 150,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
  }

  isAspectRatioLocked = () => false
  canResize = () => true
  canBind = () => false

  getDefaultProps(): ShadcnHydridMockupContainer['props'] {
    return {
      jsxString: '<Button>Default</Button>',
      w: 400,
      h: 150,
    }
  }

  component(shape: ShadcnHydridMockupContainer) {
    const { jsxString, w, h } = shape.props
    const editor = useEditor()
    const contentRef = useRef<HTMLDivElement>(null)

    // Basic auto-height adjustment (can be refined later)
    useEffect(() => {
      if (contentRef.current) {
        const contentWidth = contentRef.current.scrollWidth
        const contentHeight = contentRef.current.scrollHeight

        const PADDING_BUFFER_X = 32 // padding left + right (16px * 2)
        const HEADER_HEIGHT = 45 // Approx height of the header div
        const PADDING_BUFFER_Y = 32 // padding top + bottom for content (16px * 2)

        const newWidth = Math.max(400, contentWidth + PADDING_BUFFER_X) // Ensure min width of 400
        const newHeight = contentHeight + HEADER_HEIGHT + PADDING_BUFFER_Y

        if (Math.abs(newWidth - w) > 1 || Math.abs(newHeight - h) > 1) {
          editor.updateShape({
            id: shape.id,
            type: shape.type,
            props: { 
              w: newWidth,
              h: newHeight 
            },
          })
        }
      }
    }, [jsxString, editor, shape.id, shape.type]) // Rerun when content or width changes

    return (
      <HTMLContainer style={{ pointerEvents: 'all'}}> {/* Allow pointer events for interaction */}
        <div 
          style={{ 
            width: w,
            minHeight: h, // Use minHeight for visual rendering
            background: 'red',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.6',
            border: '1px solid #e1e4e8',
            overflow: 'hidden', // Hide overflow initially
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* Consider adding a header later if needed */}
          <div 
            ref={contentRef} // Ref for height measurement
            style={{ 
              padding: '16px', 
              //flexGrow: 1 // Allow content to fill space
              display: 'flex',
              flexDirection: 'row',
              gap: '16px',
              overflowX: 'auto'
            }}
          >
            <JsxParser
              bindings={{}}
              className="flex-row flex gap-4 overflow-x-auto p-4"
              components={availableComponents as any}
              jsx={jsxString}
               
              onError={(error) => console.error("JsxParser Error:", error)}
            />
          </div>
        </div>
      </HTMLContainer>
    )
  }

  indicator(shape: ShadcnHydridMockupContainer) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 