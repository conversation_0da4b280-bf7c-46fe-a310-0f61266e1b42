import { BaseBoxShapeUtil, H<PERSON><PERSON>ontainer, TLBaseShape, useEditor } from 'tldraw'
import { useEffect, useRef } from 'react'
import { Orbit } from 'lucide-react';
import { Iterate } from '../components/Iterate';
import { TLShapeId } from 'tldraw';

export type HTMLDesignMockupsShape = TLBaseShape<
  'design-mockups',
  {
    html: string
    w: number
    h: number
    loading: boolean
  }
>

export class HTMLDesignMockupsUtil extends BaseBoxShapeUtil<HTMLDesignMockupsShape> {
  static type = 'html-design-mockups' as const
  static props = {
    html: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 400,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 100,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    loading: {
      type: 'boolean',
      default: false,
      validate: (value: any) => typeof value === 'boolean'
    }
  }

  isAspectRatioLocked = () => false
  canResize = () => true
  canBind = () => false

  getDefaultProps(): HTMLDesignMockupsShape['props'] {
    return {
      html: '',
      w: 400,
      h: 100,
      loading: false
    }
  }

  component(shape: HTMLDesignMockupsShape) {
    const { html, w, h, loading } = shape.props
    const editor = useEditor()
    const contentRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      if (contentRef.current) {
        const contentWidth = contentRef.current.scrollWidth
        const contentHeight = contentRef.current.scrollHeight

        const PADDING_BUFFER_X = 16 // padding left + right (16px * 2)
        const HEADER_HEIGHT = 45 // Approx height of the header div
        const PADDING_BUFFER_Y = 16 // padding top + bottom for content (16px * 2)

        const newWidth = Math.max(400, contentWidth + PADDING_BUFFER_X) // Ensure min width of 400
        const newHeight = contentHeight + HEADER_HEIGHT + PADDING_BUFFER_Y

        if (Math.abs(newWidth - w) > 1 || Math.abs(newHeight - h) > 1) {
          editor.updateShape({
            id: shape.id,
            type: shape.type,
            props: {
              w: newWidth,
              h: newHeight
            },
          })
        }
      }
    }, [html, editor, shape.id, shape.type]) // Rerun only when html content changes

    return (
      <div>
        <HTMLContainer>
        
        <div
          style={{
            pointerEvents: 'all',
            width: w,
            minHeight: h,
            background: loading ? '#f6f8fa' : 'transparent',
            borderRadius: '0',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.6',
            cursor: 'grab',
            userSelect: 'none',
            border: '1px solid #e1e4e8',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #e1e4e8',
            background: '#e70076',
            color: 'white',
            fontWeight: 600,
            borderRadius: '0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            flexShrink: 0
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Design Mockups
          </div> */}
          {loading ? (
            <div
              style={{
                padding: '16px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '300px',
                minWidth: '300px',
                background: '#f6f8fa'
              }}
            >
              <Orbit className="animate-spin" />
            </div>
          ) : (
            <div
              ref={contentRef}
              style={{
                padding: '16px 0 16px 16px',
                display: 'flex',
                flexDirection: 'row',
                gap: '16px',
                overflowX: 'auto',
              }}
              dangerouslySetInnerHTML={{ __html: html }}
            />
          )}
        </div>
      </HTMLContainer>
      </div>
    )
  }

  indicator(shape: HTMLDesignMockupsShape) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 