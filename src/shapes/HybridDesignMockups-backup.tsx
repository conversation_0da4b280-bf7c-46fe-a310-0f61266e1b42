import { BaseBoxShapeUtil, HTMLContainer, TLBaseShape, useEditor } from 'tldraw'
import { useEffect, useRef } from 'react'
import { Orbit } from 'lucide-react';
import { Runner, importCode } from 'react-runner'
import * as Button from '@/components/ui/button'
import * as Input from '@/components/ui/input'
import * as Textarea from '@/components/ui/textarea'
import * as Label from '@/components/ui/label'
import * as Checkbox from '@/components/ui/checkbox'
import * as Card from '@/components/ui/card'
import * as Alert from '@/components/ui/alert'
import * as Badge from '@/components/ui/badge'
import * as Avatar from '@/components/ui/avatar'
import * as Separator from '@/components/ui/separator'
import * as Switch from '@/components/ui/switch'
import * as Skeleton from '@/components/ui/skeleton'
import * as Progress from '@/components/ui/progress'
import * as Select from '@/components/ui/select'
import * as Tabs from '@/components/ui/tabs'
import * as Toggle from '@/components/ui/toggle'
import * as Accordion from '@/components/ui/accordion'
import * as Menubar from '@/components/ui/menubar'
import * as Calendar from '@/components/ui/calendar'
import * as Command from '@/components/ui/command'
import * as RadioGroup from '@/components/ui/radio-group'
import * as Slider from '@/components/ui/slider'
import * as Breadcrumb from '@/components/ui/breadcrumb'
import * as Carousel from '@/components/ui/carousel'
import * as Pagination from '@/components/ui/pagination'
import * as ScrollArea from '@/components/ui/scroll-area'
import * as Table from '@/components/ui/table'
import * as lucideIcons from 'lucide-react';
import { DesignToolbar } from '@/components/DesignToolbar';
import  {string} from '../lib/htmlstring';
import { LiveProvider, LiveEditor, LivePreview, LiveError } from "react-live";



export type HybridDesignMockupsShape = TLBaseShape<
  'hybrid-design-mockups',
  {
    html: string
    researchData: string
    w: number
    h: number
    loading: boolean
  }
>

export class HybridDesignMockupsUtil extends BaseBoxShapeUtil<HybridDesignMockupsShape> {
  static type = 'hybrid-design-mockups' as const
  static props = {
    html: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    researchData: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 400,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 100,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    loading: {
      type: 'boolean',
      default: false,
      validate: (value: any) => typeof value === 'boolean'
    }
  }

  isAspectRatioLocked = () => false
  canResize = () => true
  canBind = () => false

  getDefaultProps(): HybridDesignMockupsShape['props'] {
    return {
      html: '',
      researchData: '',
      w: 400,
      h: 100,
      loading: false
    }
  }

  component(shape: HybridDesignMockupsShape) {
    const { html, researchData, w, h, loading } = shape.props
    const editor = useEditor()
    const contentRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      const timer = setTimeout(() => {
        if (contentRef.current) {
          const contentWidth = contentRef.current.scrollWidth
          const contentHeight = contentRef.current.scrollHeight

          const PADDING_BUFFER_X = 48 // padding left + right (16px * 2)
          //const HEADER_HEIGHT = 45 // Approx height of the header div
          const PADDING_BUFFER_Y = 48 // padding top + bottom for content (16px * 2)

          const newWidth = Math.max(400, contentWidth + PADDING_BUFFER_X) // Ensure min width of 400
          const newHeight = contentHeight + PADDING_BUFFER_Y

          if (Math.abs(newWidth - w) > 1 || Math.abs(newHeight - h) > 1) {
            editor.updateShape({
              id: shape.id,
              type: shape.type,
              props: {
                w: newWidth,
                h: newHeight
              },
            })
          }
        }
      }, 3000); // 2 second delay

      return () => clearTimeout(timer);
    }, [html, editor, shape.id, shape.type]) // Rerun only when html content changes

      // Effect to disable native image dragging
      useEffect(() => {
        if (contentRef.current && !loading) {
          const images = contentRef.current.getElementsByTagName('img');
          const preventDragStart = (e: DragEvent) => {
            e.preventDefault();
          };
  
          for (let i = 0; i < images.length; i++) {
            images[i].draggable = false;
            images[i].addEventListener('dragstart', preventDragStart);
          }
  
          return () => {
            // Cleanup: remove event listeners
            // Check contentRef.current again as it might be null during unmount
            if (contentRef.current) {
              const currentImages = contentRef.current.getElementsByTagName('img');
              for (let i = 0; i < currentImages.length; i++) {
                currentImages[i].removeEventListener('dragstart', preventDragStart);
              }
            }
          };
        }
      }, [html, loading]); // Rerun when html content changes or loading state changes



    const scope = {
      //html,
      // scope used by import statement
      import: {
          '@/components/ui/button': Button,
          '@/components/ui/input': Input,
          '@/components/ui/textarea': Textarea,
          '@/components/ui/label': Label,
          '@/components/ui/checkbox': Checkbox,
          '@/components/ui/card': Card,
          '@/components/ui/alert': Alert,
          '@/components/ui/badge': Badge,
          '@/components/ui/avatar': Avatar,
          '@/components/ui/separator': Separator,
          '@/components/ui/switch': Switch,
          '@/components/ui/skeleton': Skeleton,
          '@/components/ui/progress': Progress,
          '@/components/ui/select': Select,
          '@/components/ui/tabs': Tabs,
          '@/components/ui/toggle': Toggle,
          '@/components/ui/accordion': Accordion,
          '@/components/ui/menubar': Menubar,
          '@/components/ui/calendar': Calendar,
          '@/components/ui/command': Command,
          '@/components/ui/radio-group': RadioGroup,
          '@/components/ui/slider': Slider,
          '@/components/ui/breadcrumb': Breadcrumb,
          '@/components/ui/carousel': Carousel,
          '@/components/ui/pagination': Pagination,
          '@/components/ui/scroll-area': ScrollArea,
          '@/components/ui/table': Table,
          'lucide-react': lucideIcons,
          
      },
  }
      function generateId(): any {
  return Math.random().toString(36).substr(2, 9);
}

const id = generateId();
    const code = `import {useState} from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from "@/components/ui/select"
import { 
  Tabs, TabsContent, TabsList, TabsTrigger
} from "@/components/ui/tabs"
import { Toggle } from "@/components/ui/toggle"
import { 
  Accordion, AccordionContent, AccordionItem, AccordionTrigger 
} from "@/components/ui/accordion"
import { 
  Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarTrigger 
} from "@/components/ui/menubar"
import { Calendar } from "@/components/ui/calendar"
import { 
  Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList 
} from "@/components/ui/command"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { 
  Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import { 
  Carousel, CarouselContent, CarouselItem 
} from "@/components/ui/carousel"
import { 
  Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious 
} from "@/components/ui/pagination"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { 
  Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"
 import * as Lucide from 'lucide-react'; 


const App = () =>{

  return (
  <>
${html.replace(/`/g, '\\`').replace(/\$\{/g, '\\${')}
</>
  )
}; render(<App/>)
`
//console.log(code)



    return (
      <div>
        <DesignToolbar ResearchData={researchData} id={id} /> 
      <HTMLContainer>
        <div id="mock-container"
          style={{
            pointerEvents: 'all',
            width: w,
            minHeight: h,
            //background: loading ? '#f6f8fa' : 'transparent',
            //borderRadius: '0',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.6',
            cursor: 'grab',
            userSelect: 'none',
            //border: '1px solid #e1e4e8',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #e1e4e8',
            background: '#e70076',
            color: 'white',
            fontWeight: 600,
            borderRadius: '0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            flexShrink: 0
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Design Mockups
          </div> */}
          {loading ? (
            <div
              style={{
                padding: '16px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '300px',
                minWidth: '300px',
                background: '#f6f8fa'
              }}
            >
              <Orbit className="animate-spin" />
            </div>
          ) : (
            <div 
              ref={contentRef}
              style={{
                padding: '16px 0 16px 16px',
                display: 'flex',
                flexDirection: 'row',
                gap: '16px',
                overflowX: 'auto',
              }}>
              
              <Runner code={code} scope={scope} /> 
              {/* <LiveProvider code={code} scope={scope}> 
  <div className="grid grid-cols-3 gap-4">
    <LiveError className="text-red-800 bg-red-100 mt-2" />
    <LiveEditor className="font-mono" />
    <LivePreview />
    
  </div>
</LiveProvider> */}
              
            </div>
          )}
        </div>
      </HTMLContainer>
      </div>
    )
  }

  indicator(shape: HybridDesignMockupsShape) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 