import { BaseBoxShapeUtil, H<PERSON><PERSON>ontainer, TLBaseShape, useEditor } from 'tldraw'
import ReactMarkdown from 'react-markdown'
import { useEffect, useRef } from 'react'

export type DesignSpecShape = TLBaseShape<
  'design-spec',
  {
    markdown: string
    w: number
    h: number
  }
>

export class DesignSpecUtil extends BaseBoxShapeUtil<DesignSpecShape> {
  static type = 'design-spec' as const
  static props = {
    markdown: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 400,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 100,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
  }

  isAspectRatioLocked = () => false
  canResize = () => true
  canBind = () => false

  getDefaultProps(): DesignSpecShape['props'] {
    return {
      markdown: '',
      w: 400,
      h: 100,
    }
  }

  component(shape: DesignSpecShape) {
    const { markdown, w, h } = shape.props
    const editor = useEditor()
    const containerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      if (containerRef.current) {
        const currentHeight = containerRef.current.scrollHeight
        const PADDING_BUFFER = 32 // Approx padding top + bottom
        const newHeight = currentHeight + PADDING_BUFFER
        if (Math.abs(newHeight - h) > 1) { // Update only if height differs significantly
          editor.updateShape({
            id: shape.id,
            type: shape.type,
            props: { h: newHeight },
          })
        }
      }
    }, [w, markdown, editor, shape.id, shape.type]) // Rerun when width or content changes, REMOVED h

    return (
      <HTMLContainer>
        <div 
          ref={containerRef}
          style={{ 
            width: w,
            minHeight: h,
            background: '#ffffff',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.6',
            cursor: 'grab',
            userSelect: 'none',
            border: '1px solid #e1e4e8',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #e1e4e8',
            background: '#8a4aff',
            color: 'white',
            fontWeight: 600,
            borderRadius: '8px 8px 0 0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Design Specification
          </div>
          <div style={{ padding: '16px' }}>
            <ReactMarkdown>{markdown}</ReactMarkdown>
          </div>
        </div>
      </HTMLContainer>
    )
  }

  indicator(shape: DesignSpecShape) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 