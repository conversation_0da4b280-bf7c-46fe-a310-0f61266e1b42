import { BaseBoxShapeUtil, HTMLContainer, TLBaseShape, useEditor } from 'tldraw'
import ReactMarkdown from 'react-markdown'
import { useEffect, useRef } from 'react'
import { Brain, GraduationCap, Orbit } from 'lucide-react'
 



export type ResearchResultsShape = TLBaseShape<
  'research-results',
  {
    markdown: string
    w: number
    h: number
    loading: boolean
  }
>

export class ResearchResultsUtil extends BaseBoxShapeUtil<ResearchResultsShape> {
  static type = 'research-results' as const
  static props = {
    markdown: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 600,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 100,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    loading: {
      type: 'boolean',
      default: false,
      validate: (value: any) => typeof value === 'boolean'
    }
  }

  isAspectRatioLocked = () => false
  canResize = () => true
  canBind = () => false

  getDefaultProps(): ResearchResultsShape['props'] {
    return {
      markdown: '',
      w: 600,
      h: 100,
      loading: false
    }
  }

  component(shape: ResearchResultsShape) {
    const { markdown, w, h, loading } = shape.props
    const editor = useEditor()
    const containerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      if (containerRef.current) {
        const currentHeight = containerRef.current.scrollHeight
        const PADDING_BUFFER = 32 // Approx padding top + bottom
        const newHeight = currentHeight + PADDING_BUFFER
        if (Math.abs(newHeight - h) > 1) { // Update only if height differs significantly
          editor.updateShape({
            id: shape.id,
            type: shape.type,
            props: { h: newHeight },
          })
        }
      }
    }, [w, markdown, editor, shape.id, shape.type]) // Rerun when width or content changes, REMOVED h

    return (
      <HTMLContainer>
        <div 
          ref={containerRef}
          style={{ 
            width: w,
            minHeight: h,
            background: '#f8f9fa',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.6',
            cursor: 'grab',
            userSelect: 'none',
            border: '1px solid #e9ecef',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #e9ecef',
            background: '#0a0a0a',
            color: 'white',
            fontWeight: 600,
            borderRadius: '8px 8px 0 0',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            flexShrink: 0
          }}>
            <GraduationCap className="w-4 h-4" />
            UX Research Results
          </div>
          {loading ? (
            <div style={{ 
              padding: '16px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '300px',
              minWidth: '300px',
              background: '#f8f9fa'
            }}>
              <Orbit className="animate-spin" />
            </div>
          ) : (
            <div style={{ padding: '16px' }}>
              <ReactMarkdown>{markdown}</ReactMarkdown>
            </div>
          )}
        </div>
      </HTMLContainer>
    )
  }

  indicator(shape: ResearchResultsShape) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 