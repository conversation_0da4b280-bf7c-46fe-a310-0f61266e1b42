import { BaseBoxShapeUtil, <PERSON><PERSON><PERSON><PERSON><PERSON>, TLBaseShape, createShapeId } from 'tldraw'

// Define the shape's properties
export type ChatResponseShape = TLBaseShape<
  'chat-response',
  {
    text: string
    w: number
    h: number
  }
>

// Create the shape util
export class ChatResponseUtil extends BaseBoxShapeUtil<ChatResponseShape> {
  static type = 'chat-response' as const
  static props = {
    text: {
      type: 'string',
      default: '',
      validate: (value: any) => typeof value === 'string'
    },
    w: {
      type: 'number',
      default: 400,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
    h: {
      type: 'number',
      default: 100,
      validate: (value: any) => typeof value === 'number' && value > 0
    },
  }

  override isAspectRatioLocked = () => false
  override canResize = () => true
  override canBind = () => false

  getDefaultProps(): ChatResponseShape['props'] {
    return {
      text: '',
      w: 400,
      h: 100,
    }
  }

  component(shape: ChatResponseShape) {
    const { text, w, h } = shape.props

    return (
      <HTMLContainer>
        <div 
          style={{ 
            width: w,
            minHeight: h,
            padding: '12px',
            background: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            fontFamily: 'inherit',
            fontSize: '14px',
            lineHeight: '1.5',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            cursor: 'grab',
            userSelect: 'none',
          }}
        >
          {text}
        </div>
      </HTMLContainer>
    )
  }

  indicator(shape: ChatResponseShape) {
    return <rect width={shape.props.w} height={shape.props.h} rx={8} />
  }
} 