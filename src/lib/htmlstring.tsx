export const string = `<div class="navbar bg-base-100 shadow-xl">
        <div class="navbar-start">
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
                    </svg>
                </div>
                <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                    <li><a href="#features">Features</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="#about">About</a></li>
                </ul>
            </div>
            <div class="flex items-center space-x-2">
                <div class="avatar">
                    <div class="w-8 rounded bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
                        <i data-lucide="brain" class="w-5 h-5 text-white"></i>
                    </div>
                </div>
                <a class="btn btn-ghost text-xl font-bold">NeuralForge</a>
            </div>
        </div>
        <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
                <li><a href="#features" class="btn btn-ghost">Features</a></li>
                <li><a href="#pricing" class="btn btn-ghost">Pricing</a></li>
                <li><a href="#about" class="btn btn-ghost">About</a></li>
            </ul>
        </div>
        <div class="navbar-end">
            <a class="btn btn-primary">Get Started</a>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="hero min-h-screen bg-gradient-to-br from-primary/20 to-secondary/20">
        <div class="hero-content text-center">
            <div class="max-w-4xl">
                <div class="mb-8">
                    <div class="badge badge-secondary badge-lg mb-4">🚀 Now with GPT-4 Integration</div>
                </div>
                <h1 class="text-6xl font-bold mb-6">
                    Build the Future with
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
                        Neural AI
                    </span>
                </h1>
                <p class="text-xl mb-8 opacity-80">
                    Revolutionary AI-powered development platform that transforms ideas into production-ready code in minutes, not hours.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="btn btn-primary btn-lg">
                        <i data-lucide="rocket" class="w-5 h-5 mr-2"></i>
                        Start Building Free
                    </button>
                    <button class="btn btn-outline btn-lg">
                        <i data-lucide="play" class="w-5 h-5 mr-2"></i>
                        Watch Demo
                    </button>
                </div>
                
                <!-- Stats -->
                <div class="stats shadow mt-12 bg-base-200/50 backdrop-blur-lg">
                    <div class="stat">
                        <div class="stat-title">Active Developers</div>
                        <div class="stat-value text-primary">50K+</div>
                        <div class="stat-desc">Building with AI</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">Code Generated</div>
                        <div class="stat-value text-secondary">2.5M</div>
                        <div class="stat-desc">Lines per day</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">Time Saved</div>
                        <div class="stat-value">80%</div>
                        <div class="stat-desc">Average improvement</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-base-200">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold mb-4">Powerful Features</h2>
                <p class="text-xl opacity-70">Everything you need to accelerate your development</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Feature Cards using DaisyUI -->
                <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="code-2" class="w-8 h-8 text-white"></i>
                        </div>
                        <h2 class="card-title text-2xl">AI Code Generation</h2>
                        <p>Transform natural language into production-ready code across 50+ languages and frameworks.</p>
                        <div class="card-actions justify-end">
                            <div class="badge badge-primary">Popular</div>
                            <div class="badge badge-outline">50+ Languages</div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-gradient-to-r from-secondary to-accent rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        </div>
                        <h2 class="card-title text-2xl">Smart Testing</h2>
                        <p>Automated test generation and execution with intelligent coverage analysis and bug detection.</p>
                        <div class="card-actions justify-end">
                            <div class="badge badge-secondary">AI-Powered</div>
                            <div class="badge badge-outline">100% Coverage</div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-gradient-to-r from-accent to-primary rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="cloud" class="w-8 h-8 text-white"></i>
                        </div>
                        <h2 class="card-title text-2xl">Cloud Deploy</h2>
                        <p>One-click deployments with auto-scaling, monitoring, and rollback across all major platforms.</p>
                        <div class="card-actions justify-end">
                            <div class="badge badge-accent">Enterprise</div>
                            <div class="badge badge-outline">Multi-Cloud</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-12">What Developers Say</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center mb-4">
                            <div class="avatar">
                                <div class="w-12 rounded-full bg-gradient-to-r from-primary to-secondary"></div>
                            </div>
                            <div class="ml-4">
                                <div class="font-bold">Sarah Chen</div>
                                <div class="text-sm opacity-70">Senior Developer @ TechCorp</div>
                            </div>
                        </div>
                        <p>"NeuralForge reduced our development time by 75%. The AI understands context better than anything I've used."</p>
                        <div class="rating rating-sm mt-4">
                            <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center mb-4">
                            <div class="avatar">
                                <div class="w-12 rounded-full bg-gradient-to-r from-secondary to-accent"></div>
                            </div>
                            <div class="ml-4">
                                <div class="font-bold">Marcus Rodriguez</div>
                                <div class="text-sm opacity-70">CTO @ StartupXYZ</div>
                            </div>
                        </div>
                        <p>"Game-changer for our startup. We shipped our MVP 3x faster and with better quality than expected."</p>
                        <div class="rating rating-sm mt-4">
                            <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
                            <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-primary to-secondary">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-5xl font-bold text-white mb-6">Ready to Transform Your Development?</h2>
            <p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
                Join thousands of developers who've revolutionized their workflow with AI-powered development.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="btn btn-accent btn-lg">
                    <i data-lucide="rocket" class="w-5 h-5 mr-2"></i>
                    Start Free Trial
                </button>
                <button class="btn btn-outline btn-lg text-white border-white hover:bg-white hover:text-primary">
                    Schedule Demo
                </button>
            </div>
            
            <div class="mt-8 text-white/60">
                <p>✨ No credit card required • 🚀 Deploy in 5 minutes • 💯 99.9% uptime SLA</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer footer-center p-10 bg-base-200 text-base-content">
        <aside>
            <div class="flex items-center space-x-2 mb-4">
                <div class="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                    <i data-lucide="brain" class="w-6 h-6 text-white"></i>
                </div>
                <span class="text-2xl font-bold">NeuralForge</span>
            </div>
            <p class="font-bold">
                Building the future of AI-powered development
            </p>
            <p>Copyright © 2025 - All rights reserved</p>
        </aside>
        <nav>
            <div class="grid grid-flow-col gap-4">
                <a class="btn btn-ghost btn-sm">
                    <i data-lucide="twitter" class="w-5 h-5"></i>
                </a>
                <a class="btn btn-ghost btn-sm">
                    <i data-lucide="github" class="w-5 h-5"></i>
                </a>
                <a class="btn btn-ghost btn-sm">
                    <i data-lucide="linkedin" class="w-5 h-5"></i>
                </a>
            </div>
        </nav>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add some interactive animations
        window.addEventListener('scroll', () => {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    card.style.transform = 'translateY(0)';
                    card.style.opacity = '1';
                }
            });
        });
    </script>
    `