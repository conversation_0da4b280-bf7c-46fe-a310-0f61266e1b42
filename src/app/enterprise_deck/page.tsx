'use client'

import React from 'react';
import Image from 'next/image';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { ArrowRight, Zap, Users, Scaling, Lightbulb } from 'lucide-react';

const EnterpriseDeckPage = () => {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-8">
      <div className="w-full max-w-5xl"> {/* Container to constrain the carousel width */}
        <Carousel className="w-full">
          <CarouselContent>
            {/* Slide 1: Title Slide */}
            <CarouselItem>
              <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col items-center justify-center p-16 h-[600px]"> {/* Fixed height for consistent slide size */}
                  <h2 className="text-5xl font-bold text-gray-900 mb-4">
                    Accelerate Innovation with <span className="text-blue-600">UXAi</span>
                  </h2>
                  <p className="text-2xl text-gray-700 mb-8">
                    The AI Co-Pilot for Enterprise Product Teams
                  </p>
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-4 shadow-md">
                    Request a Demo
                  </Button>
                </CardContent>
              </Card>
            </CarouselItem>

            {/* Slide 2: The Challenge */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col items-center justify-center p-12 h-[600px]">
                  <Zap className="h-16 w-16 text-amber-500 mb-6" />
                  <h3 className="text-4xl font-bold text-gray-800 mb-6">
                    The Enterprise Innovation Bottleneck
                  </h3>
                  <p className="text-xl text-gray-600 max-w-2xl text-center">
                    Traditional workflows, siloed teams, and slow iteration cycles hinder speed and scalability in product development.
                  </p>
                   <ul className="list-disc list-inside text-gray-600 space-y-2 mt-8 text-lg text-left">
                    <li>Slow time-to-market for new features.</li>
                    <li>Inefficient cross-functional collaboration.</li>
                    <li>Difficulty maintaining consistency at scale.</li>
                  </ul>
                </CardContent>
              </Card>
            </CarouselItem>

            {/* Slide 3: The Solution - UXAi */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col items-center justify-center p-12 h-[600px]">
                   <Lightbulb className="h-16 w-16 text-green-600 mb-6" />
                  <h3 className="text-4xl font-bold text-gray-800 mb-6">
                    Introducing UXAi: Your Innovation Engine
                  </h3>
                  <p className="text-xl text-gray-600 max-w-2xl text-center">
                    An AI-powered canvas and integrated toolset designed to accelerate ideation, enhance collaboration, and drive strategic efficiency.
                  </p>
                   <ul className="list-disc list-inside text-gray-600 space-y-2 mt-8 text-lg text-left">
                    <li>Rapid AI-driven mockup generation.</li>
                    <li>Seamless iteration and variation exploration.</li>
                    <li>Centralized platform for design and product teams.</li>
                  </ul>
                </CardContent>
              </Card>
            </CarouselItem>

             {/* Slide 4: Key Value Prop 1 - Speed */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col md:flex-row items-center justify-center p-12 h-[600px] gap-8">
                   <div className="md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left">
                     <Zap className="h-16 w-16 text-blue-600 mb-6" />
                     <h3 className="text-4xl font-bold text-gray-800 mb-4">
                       Accelerate Design Velocity
                     </h3>
                     <p className="text-xl text-gray-600">
                       Move from concept to high-fidelity mockups in minutes, not weeks. Drastically reduce manual tasks and free up your team's time.
                     </p>
                   </div>
                   <div className="md:w-1/2">
                      <img
                        src="https://picsum.photos/600/400?random=speed"
                        alt="Speed Illustration"
                        width={600}
                        height={400}
                        className="rounded-lg shadow-md"
                      />
                   </div>
                </CardContent>
              </Card>
            </CarouselItem>

             {/* Slide 5: Key Value Prop 2 - Collaboration */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col md:flex-row items-center justify-center p-12 h-[600px] gap-8">
                   <div className="md:w-1/2">
                       <img
                        src="https://picsum.photos/600/400?random=collaboration"
                        alt="Collaboration Illustration"
                        width={600}
                        height={400}
                        className="rounded-lg shadow-md"
                      />
                   </div>
                   <div className="md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left">
                     <Users className="h-16 w-16 text-purple-600 mb-6" />
                     <h3 className="text-4xl font-bold text-gray-800 mb-4">
                       Enhance Cross-Functional Collaboration
                     </h3>
                     <p className="text-xl text-gray-600">
                       The shared canvas fosters seamless communication and alignment between design, product, and engineering teams.
                     </p>
                   </div>
                </CardContent>
              </Card>
            </CarouselItem>

             {/* Slide 6: Key Value Prop 3 - Efficiency */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col md:flex-row items-center justify-center p-12 h-[600px] gap-8">
                   <div className="md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left">
                     <Scaling className="h-16 w-16 text-green-600 mb-6" />
                     <h3 className="text-4xl font-bold text-gray-800 mb-4">
                       Drive Strategic Resource Optimization
                     </h3>
                     <p className="text-xl text-gray-600">
                       Empower your existing talent to achieve more, enabling leaner, more agile teams focused on high-impact work.
                     </p>
                   </div>
                   <div className="md:w-1/2">
                       <img
                        src="https://picsum.photos/600/400?random=efficiency"
                        alt="Efficiency Illustration"
                        width={600}
                        height={400}
                        className="rounded-lg shadow-md"
                      />
                   </div>
                </CardContent>
              </Card>
            </CarouselItem>


            {/* Slide 7: Final CTA */}
            <CarouselItem>
               <Card className="bg-white shadow-xl border-none">
                <CardContent className="flex flex-col items-center justify-center p-16 h-[600px]">
                  <h3 className="text-4xl font-bold text-gray-900 mb-6">
                    Ready to Transform Your Workflow?
                  </h3>
                  <p className="text-xl text-gray-700 mb-8">
                    Discover how UXAi can accelerate innovation and empower your enterprise teams.
                  </p>
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-4 shadow-md">
                    Schedule Your Enterprise Consultation
                  </Button>
                </CardContent>
              </Card>
            </CarouselItem>

          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
    </div>
  );
};

export default EnterpriseDeckPage;