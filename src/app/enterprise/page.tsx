'use client'

import React from 'react';
import Image from 'next/image'; // Import next/image
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>Right, Zap, Users, Scaling, Lightbulb, CheckCircle } from 'lucide-react';

const EnterprisePage = () => {
  return (
    <div className="min-h-screen bg-white text-gray-900">
      {/* Navigation */}
      <nav className="p-4 sm:p-6 sticky top-0 z-50 bg-white/80 backdrop-blur-lg shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            UXAi
          </h1>
          <Button variant="outline" className="border-gray-300 text-gray-900 hover:bg-gray-100">
            Contact Sales
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 sm:py-28 text-center bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 text-gray-900 leading-tight">
            Accelerate Innovation Across Your Enterprise with <span className="text-blue-600">UXAi</span>
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl text-gray-600 mb-10 max-w-3xl mx-auto">
            Empower your design, product, and engineering teams with an AI co-pilot designed for speed, collaboration, and scalable impact.
          </p>
          <div className="space-x-0 sm:space-x-4 space-y-4 sm:space-y-0">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-6 w-full sm:w-auto shadow-lg">
              Request a Demo <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" size="lg" className="border-gray-300 text-gray-900 hover:bg-gray-100 text-lg px-8 py-6 w-full sm:w-auto shadow-md">
              Explore Features
            </Button>
          </div>
        </div>
      </section>

      {/* The Enterprise Challenge Section */}
      <section className="py-16 sm:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold mb-4 text-gray-800">The Modern Enterprise Bottleneck</h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              In today's fast-paced market, traditional product development cycles struggle to keep up. Siloed teams, slow iterations, and a disconnect between vision and execution hinder growth and innovation.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
              <Zap className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">Slow Time-to-Market</h4>
              <p className="text-gray-600 text-sm">Lengthy design and approval cycles delay critical product launches.</p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
              <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">Cross-Functional Friction</h4>
              <p className="text-gray-600 text-sm">Misalignments between design, product, and engineering lead to rework and inefficiencies.</p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
              <Scaling className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">Difficulty Scaling Innovation</h4>
              <p className="text-gray-600 text-sm">Maintaining design consistency and fostering innovation across growing teams is a constant challenge.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-16 sm:py-20 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-700">
              UXAi: The Enterprise Innovation Engine
            </h3>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Our AI-powered canvas and integrated toolset are engineered to dismantle silos, supercharge productivity, and embed innovation directly into your enterprise workflows.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-10 lg:gap-16 items-start">
            <div className="bg-white p-8 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <Zap className="h-8 w-8 text-green-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Accelerate Team Velocity & Efficiency</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Move from concept to high-fidelity mockups in minutes, not weeks. Our AI drastically reduces manual design tasks, freeing your teams to focus on strategic problem-solving and creativity.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2">
                <li>Rapid AI-driven ideation and prototyping.</li>
                <li>Automated generation of design variations.</li>
                <li>Streamlined handoff with production-ready insights.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <Users className="h-8 w-8 text-green-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Forge a Cross-Functional Collaboration Hub</h4>
              </div>
              <p className="text-gray-600 mb-3">
                The interactive canvas becomes a shared source of truth. Product Managers can visually articulate requirements, designers can iterate with instant feedback, and developers gain clearer context.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2">
                <li>Centralized platform for design, ideation, and feedback.</li>
                <li>Enhanced alignment between PMs, UX, and Engineering.</li>
                <li>Visual communication to reduce misunderstandings.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <Scaling className="h-8 w-8 text-green-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Drive Strategic Resource Optimization</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Empower your existing talent to achieve more. By automating repetitive tasks and speeding up workflows, our platform allows for leaner, more agile teams that can tackle more projects with higher impact.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2">
                <li>Maximize output of current design and product teams.</li>
                <li>Reduce dependency on extensive external resources for initial concepts.</li>
                <li>Focus human expertise on high-value strategic work.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md">
              <div className="flex items-center mb-4">
                <Lightbulb className="h-8 w-8 text-green-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Embed Scalable Innovation</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Foster a culture of rapid experimentation and continuous improvement. Quickly test new ideas, gather feedback, and iterate without the traditional overhead, enabling innovation at scale across all product lines.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2">
                <li>Facilitate A/B testing of multiple design concepts.</li>
                <li>Enable quick pivots based on AI-generated insights.</li>
                <li>Maintain design consistency and quality as you scale.</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* Key Enterprise Benefits/Features Section */}
      <section className="py-16 sm:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sky-600 to-cyan-700">Unlock Tangible Business Value</h3>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              UXAi isn't just a tool; it's a catalyst for organizational transformation, delivering measurable improvements across your product lifecycle.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="h-10 w-10 text-sky-600 mb-4" />,
                title: "Skyrocket Design Output",
                description: "Generate more concepts, iterate faster, and deliver higher quality designs with the same team resources, significantly boosting productivity."
              },
              {
                icon: <Users className="h-10 w-10 text-sky-600 mb-4" />,
                title: "Empower Product Managers",
                description: "Enable PMs to visually explore ideas, articulate requirements with AI-assisted clarity, and collaborate more effectively with design and dev teams."
              },
              {
                icon: <CheckCircle className="h-10 w-10 text-sky-600 mb-4" />,
                title: "Drive Design Consistency",
                description: "Maintain brand integrity and UX consistency across all digital products and platforms with AI-assisted guidelines and smart, reusable design patterns."
              },
              {
                icon: <Lightbulb className="h-10 w-10 text-sky-600 mb-4" />,
                title: "Foster Rapid Experimentation",
                description: "Quickly create and test multiple design variations to validate hypotheses, gather insights, and make data-driven product decisions with unprecedented agility."
              },
              {
                icon: <Scaling className="h-10 w-10 text-sky-600 mb-4" />,
                title: "Streamline Developer Handoff",
                description: "Generate clearer design specifications, component details, and interaction notes, reducing ambiguity and accelerating the development cycle."
              },
               {
                icon: <ArrowRight className="h-10 w-10 text-sky-600 mb-4" />, // Consider a more specific icon like 'Brain' or 'TrendingUp'
                title: "Future-Proof Your Teams",
                description: "Equip your workforce with cutting-edge AI tools, enhancing creative capabilities and preparing your organization for the next wave of product innovation."
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-gray-50 p-8 rounded-xl shadow-sm hover:shadow-blue-600/30 transition-shadow duration-300 flex flex-col items-center text-center">
                {benefit.icon}
                <h4 className="text-xl font-semibold mb-3 text-gray-800">{benefit.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Efficiency Multiplier Section */}
       <section className="py-16 sm:py-20 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-rose-600 to-pink-700">The Efficiency Multiplier: Achieve More with Your Talent</h3>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              UXAi empowers your teams to significantly increase their impact and throughput, leading to smarter resource allocation and enhanced operational leverage.
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-10 lg:gap-16 items-center">
            <div className="order-2 md:order-1 bg-white p-8 rounded-xl shadow-md">
              <h4 className="text-2xl font-semibold mb-4 text-gray-800">Unlock Latent Capacity & Elevate Focus</h4>
              <p className="text-gray-600 mb-4">
                By automating time-consuming design and iteration tasks, our AI co-pilot frees up your skilled professionals to concentrate on higher-value strategic initiatives, complex problem-solving, and groundbreaking innovation.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-3">
                <li>Enable existing teams to handle a greater volume and complexity of projects without burnout.</li>
                <li>Optimize your talent investment by shifting focus from manual execution to strategic oversight and creative direction.</li>
                <li>Reduce the need for extensive hiring to solely scale design output; instead, scale impact.</li>
                <li>Foster a more engaged and innovative workforce by removing drudgery and empowering creativity.</li>
              </ul>
               <p className="text-sm text-gray-500 mt-6 italic">
                This isn't about replacing talent; it's about amplifying its potential and impact, allowing your best minds to do their best work.
              </p>
            </div>
            <div className="order-1 md:order-2">
              {/* Consider an image that represents efficiency, growth, or amplified teamwork */}
              <div className="relative w-full h-[450px] mx-auto"> {/* Added a container for fill or fixed size */}
                <img
                  src="https://picsum.photos/600/450?random=efficiency&grayscale"
                  alt="Team Amplified"
                  width={600}
                  height={450}
                  className="rounded-lg shadow-xl object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer / Final CTA */}
      <footer className="py-12 sm:py-16 bg-gray-800 text-center text-white">
        <div className="container mx-auto px-4">
          <h3 className="text-2xl sm:text-3xl font-bold mb-6">Ready to Revolutionize Your Workflow?</h3>
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-6 shadow-lg">
            Schedule Your Enterprise Consultation
          </Button>
          <p className="text-sm text-gray-400 mt-8">
            &copy; {new Date().getFullYear()} UXAi. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default EnterprisePage;