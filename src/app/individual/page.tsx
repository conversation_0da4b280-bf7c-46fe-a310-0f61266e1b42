'use client'

import React from 'react';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Zap, Clock, Palette, Briefcase, Lightbulb, CheckCircle } from 'lucide-react';

const IndividualPage = () => {
  return (
    <div className="min-h-screen bg-white text-gray-900">
      {/* Navigation */}
      <nav className="p-4 sm:p-6 sticky top-0 z-50 bg-white/80 backdrop-blur-lg shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            UXAi
          </h1>
          <div className="space-x-2">
            <Button variant="ghost" className="text-gray-700 hover:bg-gray-100">Pricing</Button>
            <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
              Sign Up
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 sm:py-28 text-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="container mx-auto px-4">
          <Sparkles className="h-16 w-16 text-blue-600 mx-auto mb-6" />
          <h2 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 text-gray-900 leading-tight">
            Unleash Your Creative Superpowers with <span className="text-blue-600">UXAi</span>
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl text-gray-700 mb-10 max-w-3xl mx-auto">
            Your AI design partner for faster ideation, stunning mockups, and a more inspired workflow. Stop wrestling with tools, start creating magic.
          </p>
          <div className="space-x-0 sm:space-x-4 space-y-4 sm:space-y-0">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-6 w-full sm:w-auto shadow-lg">
              Start Designing for Free <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" size="lg" className="border-gray-300 text-gray-900 hover:bg-gray-100 text-lg px-8 py-6 w-full sm:w-auto shadow-md">
              See Examples
            </Button>
          </div>
        </div>
      </section>

      {/* The Individual UXer's Challenge Section */}
      <section className="py-16 sm:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold mb-4 text-gray-800">Sound Familiar? The Solo Designer's Hurdles</h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              You're passionate about creating amazing user experiences, but the daily grind can be tough when you're flying solo.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <Clock className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">Endless Time Sinks</h4>
              <p className="text-gray-600 text-sm">Manually creating variations, searching for inspiration, and repetitive tasks eat into your creative time.</p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <Palette className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">The Blank Canvas Fear</h4>
              <p className="text-gray-600 text-sm">Sometimes, just getting started is the hardest part. Creative blocks can stall even the best designers.</p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <Briefcase className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold mb-2 text-gray-800">Juggling Multiple Hats</h4>
              <p className="text-gray-600 text-sm">From research to final mockups, you're doing it all. You need tools that simplify, not complicate.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-16 sm:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
              UXAi: Your Personal Design Amplifier
            </h3>
            <p className="mt-4 text-lg text-gray-700 max-w-3xl mx-auto">
              Stop the struggle, start the flow. UXAi is your AI co-pilot, built to enhance your unique creative process and supercharge your productivity.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-10 lg:gap-12 items-start">
            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-purple-200 transition-shadow">
              <div className="flex items-center mb-4">
                <Sparkles className="h-8 w-8 text-purple-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Ignite Creativity Instantly</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Beat the blank canvas. Describe your vision in plain language and watch UXAi generate diverse design concepts and mood boards in seconds.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2 text-sm">
                <li>Explore multiple directions without manual effort.</li>
                <li>Get AI-suggested color palettes and typography.</li>
                <li>Overcome creative blocks and find new inspiration.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-pink-200 transition-shadow">
              <div className="flex items-center mb-4">
                <Zap className="h-8 w-8 text-pink-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Design at Lightning Speed</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Transform ideas into high-fidelity mockups faster than ever. Iterate on designs, tweak elements, and see changes in real-time on our intuitive canvas.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2 text-sm">
                <li>Generate UI components and full screens with simple prompts.</li>
                <li>Instantly create variations and A/B test concepts.</li>
                <li>Free up hours of manual work for strategic thinking.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-purple-200 transition-shadow">
              <div className="flex items-center mb-4">
                <Lightbulb className="h-8 w-8 text-purple-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Access Built-in UX Wisdom</h4>
              </div>
              <p className="text-gray-600 mb-3">
                Design with confidence. UXAi integrates UX best practices and relevant research insights directly into your workflow, backing your decisions.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2 text-sm">
                <li>Get contextual UX tips and considerations.</li>
                <li>Ensure your designs are user-centered and effective.</li>
                <li>Spend less time researching, more time designing.</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-pink-200 transition-shadow">
              <div className="flex items-center mb-4">
                <Palette className="h-8 w-8 text-pink-600 mr-3" />
                <h4 className="text-2xl font-semibold text-gray-800">Your All-in-One Design Hub</h4>
              </div>
              <p className="text-gray-600 mb-3">
                No more juggling multiple tools. From initial idea to polished mockup, UXAi provides a seamless, integrated experience on a single powerful canvas.
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-1 pl-2 text-sm">
                <li>Intuitive drag-and-drop interface.</li>
                <li>Easy export options for your favorite tools.</li>
                <li>Focus on your craft, not on context-switching.</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* Key Benefits for Individuals Section */}
      <section className="py-16 sm:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-cyan-600">Elevate Your Craft & Career</h3>
            <p className="mt-4 text-lg text-gray-700 max-w-3xl mx-auto">
              UXAi is more than a tool; it's your partner in growth, helping you deliver exceptional work and stand out in a competitive field.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="h-10 w-10 text-teal-500 mb-4" />,
                title: "Deliver Stunning, High-Quality Designs",
                description: "Impress clients and stakeholders with polished, user-centered mockups that reflect deep UX understanding, powered by AI insights."
              },
              {
                icon: <Clock className="h-10 w-10 text-teal-500 mb-4" />,
                title: "Reclaim Your Time, Meet Deadlines",
                description: "Automate repetitive tasks and accelerate your workflow, giving you more time for strategic thinking, client communication, and work-life balance."
              },
              {
                icon: <Briefcase className="h-10 w-10 text-teal-500 mb-4" />,
                title: "Build an Impressive Portfolio, Faster",
                description: "Quickly generate diverse projects and case studies, showcasing your skills and versatility to attract better opportunities."
              },
              {
                icon: <Sparkles className="h-10 w-10 text-teal-500 mb-4" />,
                title: "Stay Ahead of the Curve",
                description: "Embrace the future of design by integrating cutting-edge AI into your skillset, making you a more valuable and forward-thinking designer."
              },
              {
                icon: <Palette className="h-10 w-10 text-teal-500 mb-4" />,
                title: "Expand Your Creative Range",
                description: "Effortlessly explore styles and concepts you might not have attempted manually, broadening your design horizons."
              },
               {
                icon: <ArrowRight className="h-10 w-10 text-teal-500 mb-4" />, // Consider a more specific icon
                title: "Focus on What You Love: Designing",
                description: "Let UXAi handle the grunt work, so you can immerse yourself in the creative aspects of design that truly excite you."
              }
            ].map((benefit, index) => (
              <div key={index} className="bg-gray-50 p-8 rounded-xl shadow-sm hover:shadow-teal-200 transition-shadow duration-300 flex flex-col items-center text-center">
                {benefit.icon}
                <h4 className="text-xl font-semibold mb-3 text-gray-800">{benefit.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing/Get Started Section */}
       <section className="py-16 sm:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 sm:mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-gray-800">Ready to Design Smarter, Not Harder?</h3>
            <p className="mt-4 text-lg text-gray-700 max-w-2xl mx-auto">
              Join UXAi today and transform your design process. Simple, affordable pricing to unlock powerful AI capabilities.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-8 lg:flex-row lg:justify-center lg:space-y-0 lg:space-x-8">
            {/* Free Tier Teaser - Optional */}
            {/* <div className="bg-white p-8 rounded-xl shadow-lg w-full max-w-md border border-gray-200 text-center">
              <h4 className="text-2xl font-bold text-gray-700 mb-2">Try It Free</h4>
              <p className="text-gray-600 mb-6 text-sm">Explore core features, no commitment.</p>
              <ul className="space-y-2 text-gray-600 mb-8 text-left text-sm">
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" /> Limited AI Generations</li>
                <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" /> Basic Canvas Access</li>
              </ul>
              <Button variant="outline" size="lg" className="w-full border-gray-300 text-gray-700 hover:bg-gray-100 py-3">
                Start Exploring
              </Button>
            </div> */}

            <div className="bg-white p-8 rounded-xl shadow-2xl w-full max-w-md border-2 border-blue-600 transform lg:scale-105">
              <div className="text-center">
                <h4 className="text-2xl font-bold text-blue-600 mb-2">Creator Plan</h4>
                <p className="text-4xl font-extrabold text-gray-900 mb-1">
                  $20<span className="text-xl font-normal text-gray-500">/month</span>
                </p>
                <p className="text-gray-600 mb-6 text-sm">Billed monthly. Cancel anytime.</p>
              </div>
              
              <ul className="space-y-3 text-gray-700 mb-8 text-left">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  <strong>50 AI Generation Credits</strong>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  Access to All Core AI Design Tools
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  Intuitive Design Canvas & Iteration Engine
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  Integrated UX Research & Best Practices
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  Option to Purchase Additional Credits
                </li>
                 <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  Community Support & Learning Resources
                </li>
              </ul>
              <Button size="lg" className="w-full bg-blue-600 hover:bg-blue-700 text-white text-lg py-3 shadow-md">
                Get Started with UXAi
              </Button>
              <p className="text-xs text-gray-500 mt-4 text-center">
                Unlock your full creative potential today!
              </p>
            </div>
          </div>
           <p className="text-center text-gray-600 mt-12">
            Working with a team or need more power? <a href="/enterprise" className="text-blue-600 hover:underline font-semibold">Check out our Enterprise solutions</a>.
          </p>
        </div>
      </section>

      {/* Footer / Final CTA */}
      <footer className="py-12 sm:py-16 bg-gray-100 text-center">
        <div className="container mx-auto px-4">
          <h3 className="text-2xl sm:text-3xl font-bold mb-6 text-gray-800">Join the UXAi Revolution.</h3>
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-6 shadow-lg">
            Sign Up for Free
          </Button>
          <p className="text-sm text-gray-500 mt-8">
            &copy; {new Date().getFullYear()} UXAi. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default IndividualPage;