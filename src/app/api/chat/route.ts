import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    console.log('Request body:', body)

    const response = await fetch('https://jaypears.app.n8n.cloud/webhook-test/e4bf249e-3fe9-42e9-a543-6502402eb282', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(body)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('N8N Error:', errorText)
      return NextResponse.json({ error: 'Failed to get response from N8N' }, { status: response.status })
    }

    const data = await response.json()
    console.log('N8N Response:', data)

    return NextResponse.json({
      research: data.research || '',
      designSpec: data.designSpec || '',
      designs: data.designs || ''
    })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 