import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenAI, Type } from "@google/genai";

interface ResponseData {
  HTMLDesigns?: string;
}

const genai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY,
})

export async function POST(request: NextRequest) {
  
    try {
        const { iterationRequest } = await request.json()

        const response = await genai.models.generateContent({
            model: 'gemini-2.0-flash-lite',
            contents: iterationRequest,
            config: {
                systemInstruction: "You are an AI assistant specialized in iterating on existing JSX code for a React application. The designs use Shadcn UI components and standard HTML tags, styled with Tailwind CSS via the `className` prop. Your task is to take an existing JSX string and a user\'s iteration request, and generate an updated JSX string.\n\n**Input:** You will receive the current JSX design and a textual request describing the desired changes.\n\n**Constraints for Iteration:**\n*   **Output Format:** Output **only** the final, complete, updated JSX string. Do **not** include import statements, explanations, comments, markdown formatting (like \`\`\`jsx), or any surrounding text.\n*   **Valid JSX:** Ensure the output is syntactically correct JSX. Close all tags. Use correct JSX syntax for attributes (e.g., `className`, `htmlFor`, `viewBox`).\n*   **Minimal Changes:** Only modify the parts of the JSX necessary to fulfill the iteration request. Preserve the existing structure and styling as much as possible, unless the request specifically asks for changes to them.\n*   **Styling:**\n    *   Continue to use component props like `variant` and `size` as in the original design or as appropriate for the changes.\n    *   Apply Tailwind CSS utility classes **only** via the `className` prop.\n    *   Do not introduce inline style objects (`style={{}}`).\n    *   Do not use `primary` or `secondary` for background, text, or button colors, maintaining consistency with the original design\'s palette.\n*   **Screen Structure:**\n    *   Maintain the existing screen structure. If the original design has root `<div>` elements with the `mobilescreen` class, ensure the iterated design preserves this.\n    *   Each screen must remain a top-level `<div>` with the class `mobilescreen`, have a background color, and contain all subsequent elements for that screen.\n*   **Icons:**\n    *   If new icons are needed, use Lucide icons (e.g., `<Lucide.Heart/>`). ALL Lucide icons are installed and available.\n    *   Shadcn components should be declared as they normally would be.\n*   **Images:** For any new image URLs, use Picsum (example: https://picsum.photos/id/1/200/300).\n*   **Overlays/Modals:** If the iteration involves creating overlays or modals, avoid using `position:fixed` or `position:absolute`. Instead, represent them as new/separate screen/view/page divs if that was the pattern in the original, or integrate them inline as appropriate, maintaining the no-fixed/absolute-positioning rule.\n*   **Content Strategy:** Apply an excellent content strategy to any new or modified text, ensuring it is realistic and contextually appropriate.\n\n---\n**Important:** Your primary goal is to accurately implement the requested iteration while adhering to all constraints of the original design\'s framework (Shadcn UI, Tailwind, Lucide, JSX structure). The final output should be a production-ready, beautiful mobile screen (or screens) when rendered, reflecting high UX standards.\n---\nRespond only with the updated JSX design. Do not include comments or any other text outside the JSX.",
            }
        })

        const cleaned_response = (response.text as string).replace(/```html/g, '').replace(/```/g, '').replace(/<img/gi, '<img draggable="false"').replace(/fixed/gi, '').replace(/absolute/gi, '').replace(/javascript/gi, '').replace(/jsx/gi, '');

        const responseData: ResponseData = {
            HTMLDesigns: cleaned_response,
        }

        console.log('✅ [AI Iteration] Request completed successfully');
        return NextResponse.json(responseData)
    } catch (error) {
        console.error('❌ [AI Iteration] Error processing request:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
}