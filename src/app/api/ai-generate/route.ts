import { NextResponse } from 'next/server'


const N8N_WEBHOOK_URL = 'https://jaypears.app.n8n.cloud/webhook/e4bf249e-3fe9-42e9-a543-6502402eb282'

interface ResponseData {
  research: string;
  HTMLDesignSpec?: string;
  HTMLDesigns?: string;
  shadcnDesignSpec?: string;
  shadcnDesigns?: string;
}

export async function POST(req: Request) {
  try {
    const { chatInput, enableHtml } = await req.json()

    if (!chatInput) {
      return NextResponse.json({ error: 'Missing chatInput' }, { status: 400 })
    }

    console.log('AI Generate API received input:', chatInput, 'enableHtml:', enableHtml)

    // --- Call the n8n webhook --- 
    const aiRequestBody = {
      prompt: chatInput,
      enableHtml: enableHtml
    }

    const aiResponse = await fetch(N8N_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(aiRequestBody),
    })

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text()
      console.error('n8n AI service error:', errorText)
      throw new Error(`AI service failed with status ${aiResponse.status}: ${errorText}`)
    }

    const data = await aiResponse.json()
    
    // Determine which set of fields to return based on enableHtml flag
    const responseData: ResponseData = {
      research: data.research || '',
    }

    if (enableHtml) {
      // Return HTML-related fields
      responseData.HTMLDesignSpec = data.HTMLDesignSpec || ''
      responseData.HTMLDesigns = data.HTMLDesigns || ''
    } else {
      // Return Shadcn-related fields
      responseData.shadcnDesignSpec = data.shadcnDesignSpec || ''
      responseData.shadcnDesigns = data.shadcnDesigns || ''
    }

    return NextResponse.json(responseData)

  } catch (error: any) {
    console.error('Error in /api/ai-generate:', error)
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON payload' }, { status: 400 })
    }
    return NextResponse.json({ error: 'Internal Server Error', details: error.message }, { status: 500 })
  }
} 