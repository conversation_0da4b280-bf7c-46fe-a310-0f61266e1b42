'use client'
import React from 'react';
import {
  Deck,
  Slide,
  Heading,
  Text,
  UnorderedList,
  ListItem,
  Image,
  Box,
  CodePane
} from 'spectacle';

// Spectacle theme (optional, using a simple default)
// A more modern and UX-focused theme
const theme = {
  colors: {
    primary: '#FFFFFF', // White text on dark backgrounds
    secondary: '#1F2937', // Dark background (Cool Gray 800)
    tertiary: '#3B82F6', // Accent blue (Blue 500)
    quaternary: '#9CA3AF', // Lighter gray for secondary text (Cool Gray 400)
    quinary: '#F59E0B', // Accent amber/yellow (Amber 500)
    // Additional colors
    background: '#111827', // Even darker background for slides (Cool Gray 900)
    heading: '#FFFFFF', // White for headings
    text: '#D1D5DB', // Light gray for body text (Cool Gray 300)
    link: '#60A5FA', // Lighter blue for links (Blue 400)
    codeBg: '#1F2937', // Background for code panes
  },
  fonts: {
    header: '"Inter", "Helvetica Neue", Helvetica, Arial, sans-serif',
    text: '"<PERSON>", "Helvetica Neue", Helvetica, Arial, sans-serif',
    monospace: '"Fira Code", "Consolas", "Monaco", "Andale Mono", "Ubuntu Mono", monospace',
  },
  fontSizes: {
    h1: '72px',
    h2: '60px',
    h3: '48px',
    h4: '36px',
    text: '28px',
    monospace: '20px',
  },
  space: [0, 8, 16, 24, 32, 40, 48, 64], // More granular spacing
  styles: {
    Slide: {
      padding: '5%', // Add some padding to slides
      background: '#111827', // Use the darker background for all slides by default
    },
    Heading: {
      color: '#FFFFFF', // Default heading color
      fontWeight: 600,
    },
    Text: {
      color: '#D1D5DB', // Default text color
    },
    UnorderedList: {
      listStyleType: 'square', // Example customization
      color: '#D1D5DB',
    },
    ListItem: {
      marginBottom: '16px',
      lineHeight: '1.6',
    }
  },
};

const Presentation = () => (
  <Deck theme={theme}>
    {/* Title Slide */}
    <Slide backgroundColor="secondary">
      <Heading color="tertiary" fontSize="h1" textAlign="center" lineHeight={1.2}>
        Unleash UX Superpowers: <br />Design at the Speed of Thought
      </Heading>
      <Text color="quaternary" fontSize="h3" textAlign="center" margin="32px 0 0">
        The AI Co-Pilot for Rapid, Insight-Driven UX/UI Design
      </Text>
    </Slide>

    {/* Problem Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">The Bottleneck in Brilliance</Heading>
      <UnorderedList className="space-y-4">
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">The "Need for Speed": Designers are constantly battling the clock, struggling to deliver exceptional UX under tight deadlines.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">The "Iteration Labyrinth": Manual ideation and refinement cycles are slow, draining creative energy and delaying innovation.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">The "Research-to-Reality Gap": Bridging the chasm between valuable user insights, best practices, and actual design implementation is a persistent hurdle.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">The "Fragmented Toolkit": Designers juggle disparate tools for AI generation, research, and iteration, hindering a fluid workflow.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">The "Cost of Inefficiency": Manual, slow iteration doesn't just waste time; it means missing out on significant usability gains (studies show iterative design can improve usability by a median of 165%).</div></ListItem>
      </UnorderedList>
    </Slide>

    {/* Solution Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">Introducing [Your Project Name]: The UX Accelerator</Heading>
      <Text>Our AI-powered platform transforms the design workflow:</Text>
      <UnorderedList className="space-y-4">
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">Instantly Generate Diverse Mockups: Go from idea to visual concept in seconds using natural language.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">Design with Confidence: Seamlessly integrate AI-researched UX best practices and relevant user insights directly into your workflow.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">Iterate at Lightning Speed: Explore variations and refine designs on an intuitive, interactive canvas.</div></ListItem>
        <ListItem><div className="p-3 rounded-md hover:bg-gray-700 transition-colors">Scalable for Every Designer: Empowering solo creators, collaborative teams, and large enterprises.</div></ListItem>
      </UnorderedList>
      <div className="mt-8 p-6 bg-gray-800 rounded-lg shadow-md">
        <Heading fontSize="h4" color="tertiary" margin="0 0 12px 0">Clear & Scalable Monetization</Heading>
        <Text color="text" fontSize="24px" margin="0 0 8px 0">
          Our model is built on a flexible, value-driven credit system:
        </Text>
        <UnorderedList className="list-inside space-y-2 ml-4">
          <ListItem><Text color="text" fontSize="24px"><strong>Core Subscription:</strong> Starting at an accessible $20/month for 50 generation credits.</Text></ListItem>
          <ListItem><Text color="text" fontSize="24px"><strong>Pay-As-You-Grow:</strong> Options to easily purchase additional credits as needed.</Text></ListItem>
          <ListItem><Text color="text" fontSize="24px"><strong>Future Tiers:</strong> Plans to introduce tiered subscriptions with more features and credits for power users and enterprise teams.</Text></ListItem>
        </UnorderedList>
        <Text color="quaternary" fontSize="20px" fontStyle="italic" mt={3}>
          This approach ensures affordability for individual designers while offering scalability for larger teams and heavier usage.
        </Text>
      </div>
    </Slide>

    {/* Solution - Features Showcase (Optional: Add code snippets or images) */}
    <Slide>
      <Heading fontSize="h3" color="heading" margin="0 0 32px 0">Key Features</Heading>
      <div className="flex flex-row justify-around items-start h-full gap-8">
        <div className="w-1/2 bg-gray-800 p-6 rounded-lg shadow-xl flex flex-col items-center text-center">
          <Heading fontSize="h4" color="heading" margin="0 0 16px 0">Intuitive AI Chat Interface</Heading>
          <Text fontSize="24px" color="text" margin="0 0 24px 0">Simply describe your UI needs, and watch as initial designs and supporting research materialize on the canvas. Effortless ideation starts here.</Text>
          {/* Placeholder: Image of a clean, modern chat interface next to a generated UI mockup on a canvas */}
          <Image src="https://picsum.photos/350/250?random=1&description=AIChatViewWithMockup" alt="AI Chat View with Generated Mockup" style={{ borderRadius: '8px' }} />
        </div>
        <div className="w-1/2 bg-gray-800 p-6 rounded-lg shadow-xl flex flex-col items-center text-center">
          <Heading fontSize="h4" color="heading" margin="0 0 16px 0">Dynamic Iteration Engine</Heading>
          <Text fontSize="24px" color="text" margin="0 0 24px 0">Select any generated element and instantly explore variations, apply different styles, or request specific modifications. Fine-tune your vision with AI assistance.</Text>
          {/* Placeholder: Image of a UI mockup on a canvas with an iteration/variation panel or overlay visible */}
          <Image src="https://picsum.photos/350/250?random=2&description=IterationToolInAction" alt="Iteration Tool with Variations" style={{ borderRadius: '8px' }} />
        </div>
      </div>
    </Slide>

    {/* Market Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">The Opportunity: A Thriving Market Ready for AI</Heading>
      <UnorderedList>
        <ListItem color="text">
          Explosive Growth in UX: The global UX services market is skyrocketing towards <Text as="span" color="quinary" fontWeight="bold">$54.93B by 2032</Text> (<Text as="span" color="quinary" fontWeight="bold">36% CAGR</Text>), with UI/UX software hitting <Text as="span" color="quinary" fontWeight="bold">$10.7B by 2033</Text>. This signals massive demand.
        </ListItem>
        <ListItem color="text">
          AI is Reshaping Design: The AI in graphic design sector is set for <Text as="span" color="quinary" fontWeight="bold">~28% annual growth</Text>. Crucially, <Text as="span" color="quinary" fontWeight="bold">78%</Text> of experienced designers are already embracing AI, seeking tools that augment, not replace, their skills.
        </ListItem>
        <ListItem color="text">The Sweet Spot: Our solution sits at the intersection of this booming UX market and the rapid adoption of AI, addressing a clear need for intelligent, efficiency-boosting design tools.</ListItem>
        <ListItem color="text">
          Investors are Taking Note: Significant funding rounds (e.g., <Text as="span" color="quinary" fontWeight="bold">$1M-$10M+</Text>) for AI design tool startups (by firms like Accel, Sequoia) and dedicated funds/grants signal strong market confidence and opportunity.
        </ListItem>
      </UnorderedList>
    </Slide>

    {/* Founder Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">The Visionary: Why Joseph Peters?</Heading>
      <div className="flex flex-row justify-between items-start gap-12">
        <div className="w-2/3 bg-gray-800 p-6 rounded-lg shadow-lg">
          <Heading fontSize="h3" color="heading" textAlign="left" margin="0 0 16px 0">Joseph Peters</Heading>
          <Text fontSize="h4" color="tertiary" textAlign="left" margin="0 0 24px 0">Director Of Strategy Product & User Experience</Text>
          <UnorderedList className="space-y-3">
            <ListItem><div className="text-gray-300">20+ Years Driving Digital Innovation: A seasoned UX professional with a rich history in design thinking, interaction design, and usability across diverse platforms.</div></ListItem>
            <ListItem><div className="text-gray-300">Fortune 500 & Major Brand Impact: Proven success developing and designing projects for top-tier clients (e.g., Boost, Columbia Records, 20th Century Fox).</div></ListItem>
            <ListItem><div className="text-gray-300">Leadership in High-Stakes UX: Currently leading strategy and UX at Elevancehealth, demonstrating expertise in complex, user-centric solutions.</div></ListItem>
            <ListItem><div className="text-gray-300">Master of User & Business Needs: Uniquely skilled at aligning user empathy with strategic business objectives to deliver impactful products.</div></ListItem>
            <ListItem><div className="text-gray-300">The Right Founder: Deep industry knowledge and a passion for empowering designers make Joseph uniquely positioned to lead this venture to success.</div></ListItem>
          </UnorderedList>
        </div>
        <div className="w-1/3 flex flex-col items-center text-center pt-6">
          {/* Placeholder for a professional headshot */}
          <Image src="https://picsum.photos/250/250?random=3&description=JosephPetersHeadshot" alt="Joseph Peters Headshot" className="rounded-full shadow-md mb-4" />
          <Text fontSize="24px" color="heading" fontWeight="bold">Joseph Peters</Text>
        </div>
      </div>
    </Slide>

{/* Roadmap Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">Our Exciting Roadmap Ahead</Heading>
      <Text color="quaternary" margin="0 0 24px 0">Key features currently in development to further enhance the platform:</Text>
      <div className="flex flex-row justify-around items-start mt-8 gap-8">
        <div className="w-1/2 bg-gray-800 p-6 rounded-lg shadow-lg">
          <UnorderedList className="space-y-3">
            <ListItem><div className="text-gray-300">Faster Output</div></ListItem>
            <ListItem><div className="text-gray-300">History of Prompts</div></ListItem>
            <ListItem><div className="text-gray-300">Import/Export to Figma</div></ListItem>
            <ListItem><div className="text-gray-300">Refine/Revisions with AI</div></ListItem>
            <ListItem><div className="text-gray-300">Direct Canvas Editing</div></ListItem>
          </UnorderedList>
        </div>
        <div className="w-1/2 bg-gray-800 p-6 rounded-lg shadow-lg">
          <UnorderedList className="space-y-3">
            <ListItem><div className="text-gray-300">Upload Designs in Prompts</div></ListItem>
            <ListItem><div className="text-gray-300">Desktop Mockups</div></ListItem>
            <ListItem><div className="text-gray-300">Web-Sourced User Insights</div></ListItem>
            <ListItem><div className="text-gray-300">Enhanced Design Inspiration</div></ListItem>
            <ListItem><div className="text-gray-300">Robust Saving & Storage</div></ListItem>
          </UnorderedList>
        </div>
      </div>
    </Slide>
    {/* Closing Slide */}
    <Slide>
      <Heading fontSize="h2" color="heading" margin="0 0 32px 0">Let's Build the Future of UX, Together.</Heading>
      <Text fontSize="h3" margin="32px 0">We're seeking [Specify Ask: e.g., $X in seed funding / strategic partners] to accelerate our growth and empower designers worldwide.</Text>
      <div className="mt-12 bg-gray-800 p-8 rounded-lg shadow-xl inline-block">
        <Heading fontSize="h4" color="tertiary" margin="0 0 16px 0">Contact Us:</Heading>
        <Text color="text" fontSize="24px">Joseph Peters</Text>
        <Text color="text" fontSize="24px">[Your Email Address]</Text>
        <Text color="text" fontSize="24px">[Your Website/LinkedIn Profile URL (Optional)]</Text>
      </div>
      <Text mt={8} fontStyle="italic" color="quaternary">Thank You</Text>
    </Slide>

  </Deck>
);

export default Presentation;