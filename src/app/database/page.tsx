//This page uses Database persistence.
'use client'
import { Tldraw, createShapeId, TLUiComponents, TLEditorComponents, track, useEditor, Editor, TLRecord, StoreSnapshot, TLStoreWithStatus, createTLStore, loadSnapshot, getSnapshot, defaultShapeUtils } from 'tldraw'
import 'tldraw/tldraw.css'
import { ResearchResultsUtil } from '@/shapes/ResearchResults'
import { DesignSpecUtil } from '@/shapes/DesignSpec'
import { HTMLDesignMockupsUtil } from '@/shapes/HTMLDesignMockups'
import { ShadcnHydridMockupContainerUtil } from '@/shapes/ShadcnHydridMockupContainer'
import { HybridDesignMockupsUtil } from '@/shapes/HybridDesignMockups'
import { AiChatView } from '@/components/AiChatView'
import IntroSection from '@/components/IntroSection'
import { useCallback, useEffect, useRef, useState } from 'react'
import '@/app/style_overrides.css'
import { Iterate } from '@/components/Iterate'
import throttle from 'lodash.throttle'
import { supabase } from '@/lib/supabaseClient'
import { AuthWrapper } from '@/components/auth/AuthWrapper'
import { useAuth } from '@/lib/auth'

const customShapeUtils = [
  ResearchResultsUtil,
  DesignSpecUtil,
  HTMLDesignMockupsUtil,
  ShadcnHydridMockupContainerUtil,
  HybridDesignMockupsUtil,
]

const storeShapeUtils = [
  ...defaultShapeUtils,
  ...customShapeUtils,
]

// Fallback ID for anonymous users
const ANONYMOUS_ID = 'anonymous'

export default function Home() {
  const { user } = useAuth()
  // State to hold the store and its loading status
  const [storeWithStatus, setStoreWithStatus] = useState<TLStoreWithStatus>({
    status: 'loading',
  })

  // Effect for loading the initial snapshot and creating the store
  useEffect(() => {
    let cancelled = false

    async function loadOrCreateStore() {
      setStoreWithStatus({ status: 'loading' })

      let snapshot: StoreSnapshot<TLRecord> | null = null

      try {
        // Use the authenticated user's ID for persistence, or anonymous ID if not logged in
        const userId = user?.id || ANONYMOUS_ID
        console.log(`Fetching snapshot for user: ${userId}`)
        const { data, error } = await supabase.functions.invoke('get-canvas-snapshot', {
          body: { userId }, 
        })

        if (error) {
          console.error('Error fetching snapshot:', error.message)
          // Don't throw, allow creating an empty store
        } else if (data && data.snapshot_data) {
          console.log('Snapshot found, preparing to load.')
          snapshot = data.snapshot_data as StoreSnapshot<TLRecord>
        } else {
          console.log('No snapshot found or data malformed.')
          // Set snapshot to null if not found, to proceed with empty store
          snapshot = null
        }
      } catch (e: any) {
        console.error('Exception fetching snapshot:', e.message)
        if (cancelled) return
        // Set error status
        setStoreWithStatus({ status: 'error', error: e })
        return // Don't proceed after error
      }

      if (cancelled) return

      // Create the store instance
      const newStore = createTLStore({ shapeUtils: storeShapeUtils })

      // Load the snapshot if it exists
      if (snapshot) {
        try {
          // Note: `loadSnapshot` mutates the store instance
          loadSnapshot(newStore, snapshot)
          console.log('Snapshot loaded successfully into store.')
        } catch (e: any) {
          console.error("Failed to load snapshot into store:", e.message);
          if (cancelled) return
          // Set error status if loading snapshot fails
          setStoreWithStatus({ status: 'error', error: e })
          return // Don't proceed after error
        }
      } else {
        console.log('Proceeding with an empty store.')
      }

      if (cancelled) return

      // Update the state with the ready store
      setStoreWithStatus({
        status: 'synced-local', // Use appropriate status
        store: newStore,
      })
    }

    // Always load the store regardless of authentication status
    loadOrCreateStore()

    return () => {
      cancelled = true
    }
  }, [user]) // Still re-run when user changes to fetch their data

  // Effect for setting up the save listener once the store is ready
  useEffect(() => {
    // Check for the correct status before accessing the store
    if (storeWithStatus.status !== 'synced-local') return

    // Now we know store exists because status is 'synced-local'
    const store = storeWithStatus.store

    // Define the save handler using the ready store
    const handleSave = throttle(async () => {
      const snapshot = getSnapshot(store) // Use imported getSnapshot
      // Use the authenticated user's ID or anonymous ID
      const userId = user?.id || ANONYMOUS_ID
      console.log(`Saving snapshot for user: ${userId}`)
      try {
        const { error: saveError } = await supabase.functions.invoke('save-canvas-snapshot', {
          body: { userId, snapshot },
        })
        if (saveError) {
          console.error('Error saving snapshot:', saveError.message)
          // toast.error(`Failed to save canvas: ${saveError.message}`);
        } else {
          // console.log('Snapshot saved successfully.');
        }
      } catch (e: any) {
        console.error('Exception while saving snapshot:', e.message)
        // toast.error(`Exception saving canvas: ${e.message}`);
      }
    }, 1000) // Throttle saves

    // Listen to store changes for saving
    const cleanupFn = store.listen(handleSave, { source: 'user', scope: 'document' }) // Only save user document changes

    return () => {
      cleanupFn()
      handleSave.cancel() // Cancel any pending throttled calls on cleanup
    }
  }, [storeWithStatus, user]) // Re-run if storeWithStatus or user changes

  // Context Toolbar remains largely the same, uses useEditor internally
  const ContextToolbarComponent = track(() => {
    const editor = useEditor() // useEditor gets the editor instance from context
    const showToolbar = editor.isIn('select.idle')
    if (!showToolbar) return null

    const selectedShapes = editor.getSelectedShapes()
    if (selectedShapes.length !== 1) return null

    const selectedShape = selectedShapes[0]
     // Updated based on previous edit
    if (selectedShape.type !== 'html-design-mockups' && selectedShape.type !== 'hybrid-design-mockups') return null 

    const isLoading = (selectedShape as any).props.loading
    if (isLoading) return null

    const selectionRotatedPageBounds = editor.getSelectionRotatedPageBounds()
    if (!selectionRotatedPageBounds) return null

    const pageCoordinates = editor.pageToViewport(selectionRotatedPageBounds.point)
    const shapeId = selectedShape.id
    const html = (selectedShape as any).props.html

    return (
      <div
        style={{
          position: 'absolute',
          pointerEvents: 'all',
          top: pageCoordinates.y - 80,
          left: pageCoordinates.x + 0,
          width: selectionRotatedPageBounds.width * editor.getZoomLevel(),
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPointerDown={(e) => e.stopPropagation()}
      >
        <div
          style={{
            borderRadius: 8,
            display: 'flex',
            background: '#fff',
            width: 'fit-content',
            alignItems: 'center',
          }}
        >
          <div className="flex flex-row gap-2 min-h-[44px] max-h-[44px] px-4 overflow-hidden rounded-lg shadow-lg bg-white dark:bg-gray-800 items-center justify-between"
            style={{ width: 'clamp(300px, 90vw, 600px)'}}>
            <span className="text-sm font-bold">Mockup</span>
            <div className="flex flex-row gap-2">
              <Iterate currentHtml={html} shapeId={shapeId} />
            </div>
          </div>
        </div>
      </div>
    )
  })

  const components: Partial<TLUiComponents & TLEditorComponents> = {
    StylePanel: null,
    HelpMenu: null,
    //MenuPanel: null,
    ActionsMenu: null,
    Toolbar: null,
  }

  const components2: TLEditorComponents = {
    InFrontOfTheCanvas: ContextToolbarComponent,
  }

  const allComponents = {
    ...components,
    ...components2,
  }

  return (
    <AuthWrapper requireAuth={false}>
      <div style={{ position: 'fixed', inset: 0 }}>
        <Tldraw
          shapeUtils={storeShapeUtils}
          // Pass the store (with status)
          store={storeWithStatus}
          // Pass shape utils here if not passed during store creation
          // shapeUtils={customShapeUtils}
          components={allComponents}
          // We can still use onMount for editor instance configuration
          onMount={(editor) => {
            editor.updateInstanceState({ isGridMode: true })
          }}
        >
          <AiChatView />
        </Tldraw>
      </div>
    </AuthWrapper>
  )
}

