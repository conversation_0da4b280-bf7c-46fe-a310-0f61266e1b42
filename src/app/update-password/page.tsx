'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabaseClient'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { Loader2, Check } from 'lucide-react'

export default function UpdatePassword() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  // Check if we have an access token in the URL
  useEffect(() => {
    const handlePasswordReset = async () => {
      const { error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('Error getting session:', error.message)
        setMessage('Invalid or expired password reset link. Please try again.')
      }
    }
    
    handlePasswordReset()
  }, [])

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (password !== confirmPassword) {
      setMessage('Passwords do not match')
      return
    }
    
    if (password.length < 6) {
      setMessage('Password must be at least 6 characters')
      return
    }

    try {
      setLoading(true)
      
      const { error } = await supabase.auth.updateUser({ 
        password 
      })
      
      if (error) {
        throw error
      }
      
      setSuccess(true)
      setMessage('')
      toast.success('Password updated successfully')
      
      // Redirect after a short delay
      setTimeout(() => {
        router.push('/')
      }, 2000)
      
    } catch (error: any) {
      console.error('Update password error:', error.message)
      setMessage(error.message || 'Error updating password')
      toast.error(error.message || 'Error updating password')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Update Password</h1>
          <p className="text-gray-500 dark:text-gray-400">
            {success 
              ? 'Your password has been updated successfully!' 
              : 'Please enter your new password below.'}
          </p>
        </div>

        {success ? (
          <div className="flex flex-col items-center justify-center gap-4 p-4">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
              <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
            </div>
            <p className="text-center text-gray-600 dark:text-gray-300">
              You'll be redirected to the sign-in page shortly...
            </p>
          </div>
        ) : (
          <form onSubmit={handlePasswordUpdate} className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  New Password
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your new password"
                />
              </div>
              
              <div className="space-y-2">
                <label htmlFor="confirm-password" className="text-sm font-medium">
                  Confirm New Password
                </label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  placeholder="Confirm your new password"
                />
              </div>
            </div>
            
            {message && (
              <p className="text-sm font-medium text-red-500">{message}</p>
            )}
            
            <Button 
              type="submit" 
              className="w-full" 
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Password...
                </>
              ) : (
                'Update Password'
              )}
            </Button>
          </form>
        )}
      </div>
    </div>
  )
} 