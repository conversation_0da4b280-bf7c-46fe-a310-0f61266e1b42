export const designSpecExample = `
# UI Specification: Sign Up - Vitality Tracker

This document details the UI for the Vitality Tracker mobile sign-up page.

## 1. Page: Sign Up - Vitality Tracker

**Purpose:** Allows new users to create an account to access the Vitality Tracker application.
**Container:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Padding:** \`24px\` horizontal padding from screen edges. Vertical padding as needed for content flow, starting \`40px\` from the top safe area.

## 2. Header

**Purpose:** Identifies the application.
**Type:** Text/App Name
**Content:** \`Vitality Tracker\`
**Visual Properties:**
*   **Font:** System Font, Bold
*   **Font Size:** \`28px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Alignment:** Center horizontally
*   **Spacing:** \`40px\` below the top edge of the container.

## 3. Sign-Up Title

**Purpose:** Clearly states the purpose of the page.
**Type:** Text
**Content:** \`Create Your Account\`
**Visual Properties:**
*   **Font:** System Font, Semibold
*   **Font Size:** \`24px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`32px\` below the Header.

## 4. Sign-Up Description

**Purpose:** Provides a brief value proposition.
**Type:** Text
**Content:** \`Start tracking your health journey today.\`
**Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color:** \`#5E5E5E\` (Dark Gray)
*   **Spacing:** \`8px\` below the Sign-Up Title.

## 5. Email Input Field

**Purpose:** Allows the user to enter their email address.
**Container:**
*   **Background Color:** \`#F5F5F5\` (Light Gray)
*   **Border Radius:** \`8px\`
*   **Padding:** \`14px\` vertical, \`16px\` horizontal.
*   **Spacing:** \`32px\` below the Sign-Up Description.

**Element: Label**
*   **Purpose:** Identifies the input field.
*   **Content:** \`Email Address\`
*   **Visual Properties:**
*   **Font:** System Font, Medium
*   **Font Size:** \`14px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`0px\` relative to the top of the input container (placed above the input text area).

**Element: Text Input Area**
*   **Purpose:** User enters email here.
*   **Content:** (User Input)
*   **Placeholder Content:** \`e.g., <EMAIL>\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color (Input):** \`#1A1A1A\` (Near Black)
*   **Font Color (Placeholder):** \`#A9A9A9\` (Light Gray)
*   **Spacing:** \`4px\` below the Label.
*   **Keyboard Type:** \`email-address\`
*   **Autocomplete:** \`email\`
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray) in default state. \`1px\` solid \`#007AFF\` (Primary Blue) in focus state.
*   **Error State Example:** If validation fails, border changes to \`1px\` solid \`#FF3B30\` (Red), and an error message appears below the input field.

**Element: Error Message (Conditional)**
*   **Purpose:** Informs the user of input errors.
*   **Content Example:** \`Please enter a valid email address.\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`12px\`
*   **Font Color:** \`#FF3B30\` (Red)
*   **Spacing:** \`4px\` below the Email Input Field container.

## 6. Password Input Field

**Purpose:** Allows the user to create a password.
**Container:**
*   **Background Color:** \`#F5F5F5\` (Light Gray)
*   **Border Radius:** \`8px\`
*   **Padding:** \`14px\` vertical, \`16px\` horizontal.
*   **Spacing:** \`16px\` below the Email Input Field (or its error message if present).

**Element: Label**
*   **Purpose:** Identifies the input field.
*   **Content:** \`Password\`
*   **Visual Properties:**
*   **Font:** System Font, Medium
*   **Font Size:** \`14px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`0px\` relative to the top of the input container (placed above the input text area).

**Element: Text Input Area**
*   **Purpose:** User enters password here.
*   **Content:** (User Input)
*   **Placeholder Content:** \`create a strong password\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color (Input):** \`#1A1A1A\` (Near Black)
*   **Font Color (Placeholder):** \`#A9A9A9\` (Light Gray)
*   **Spacing:** \`4px\` below the Label.
*   **Keyboard Type:** \`default\`
*   **Secure Text Entry:** Enabled (dots instead of characters).
*   **Autocomplete:** \`new-password\`
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray) in default state. \`1px\` solid \`#007AFF\` (Primary Blue) in focus state.
*   **Error State Example:** If validation fails, border changes to \`1px\` solid \`#FF3B30\` (Red), and an error message appears below the input field.

**Element: Password Toggle Icon**
*   **Purpose:** Toggles password visibility.
*   **Type:** Icon Button
*   **Icon:** Eye icon (\`eye\` or \`eye-slash\` from a standard icon library like Font Awesome or Material Icons)
*   **Visual Properties:**
*   **Color:** \`#A9A9A9\` (Light Gray) in default state, \`#5E5E5E\` (Dark Gray) when active/toggled.
*   **Size:** \`20px\` x \`20px\`
*   **Position:** Aligned to the right edge of the input field, vertically centered within the input text area height.

**Element: Error Message (Conditional)**
*   **Purpose:** Informs the user of input errors.
*   **Content Example:** \`Password must be at least 8 characters.\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`12px\`
*   **Font Color:** \`#FF3B30\` (Red)
*   **Spacing:** \`4px\` below the Password Input Field container.

## 7. Create Account Button (Primary CTA)

**Purpose:** Submits the sign-up form.
**Type:** Button
**Content:** \`Create Account\`
**Visual Properties:**
*   **Background Color:** \`#007AFF\` (Primary Blue)
*   **Text Color:** \`#FFFFFF\` (White)
*   **Font:** System Font, Semibold
*   **Font Size:** \`18px\`
*   **Border Radius:** \`8px\`
*   **Padding:** \`16px\` vertical. Full width.
*   **Spacing:** \`32px\` below the Password Input Field (or its error message if present).
*   **State (Disabled):** Background Color \`#B0C4DE\` (Light Steel Blue), Text Color \`#E0E0E0\` (Very Light Gray). Button should be disabled if fields are empty or invalid.

## 8. Separator

**Purpose:** Separates the email/password form from alternative sign-up options.
**Type:** Text with lines
**Content:** \`or continue with\`
**Visual Properties:**
*   **Text Font:** System Font, Regular, \`14px\`, \`#A9A9A9\` (Light Gray)
*   **Lines:** \`1px\` solid \`#E0E0E0\` (Very Light Gray), extending horizontally to the left and right of the text.
*   **Spacing:** \`24px\` below the Create Account Button.

## 9. Social Sign-Up Buttons

**Purpose:** Provides alternative sign-up methods using third-party accounts.
**Layout:** Horizontal stack, centered, with spacing between buttons.
**Spacing:** \`16px\` below the Separator.

**Element: Google Sign-Up Button**
*   **Purpose:** Sign up via Google account.
*   **Type:** Button
*   **Content:**
*   **Icon:** Google logo (\`32px\` x \`32px\`) - full color.
*   **Text:** (No text, just icon)
*   **Visual Properties:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray)
*   **Border Radius:** \`8px\`
*   **Size:** \`48px\` x \`48px\` (Icon centered within the button)
*   **Spacing:** \`16px\` horizontal spacing from the Apple button.

**Element: Apple Sign-Up Button**
*   **Purpose:** Sign up via Apple account.
*   **Type:** Button
*   **Content:**
*   **Icon:** Apple logo (\`32px\` x \`32px\`) - black fill.
*   **Text:** (No text, just icon)
*   **Visual Properties:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray)
*   **Border Radius:** \`8px\`
*   **Size:** \`48px\` x \`48px\` (Icon centered within the button)
*   **Spacing:** \`16px\` horizontal spacing from the Google button.

*(Note: Other social options like Facebook could be added here, following a similar pattern).*

## 10. Login Link

**Purpose:** Provides navigation for users who already have an account.
**Type:** Text Link
**Content:** \`Already have an account? \` + **Link Text:** \`Log In\`
**Visual Properties:**
*   **Text Font:** System Font, Regular, \`14px\`, \`#5E5E5E\` (Dark Gray)
*   **Link Text Font:** System Font, Semibold, \`14px\`, \`#007AFF\` (Primary Blue)
*   **Spacing:** \`40px\` below the Social Sign-Up Buttons.
*   **Alignment:** Center horizontally.
`;


export const htmlDesignExample = `
Example multi-page structure:
\`\`\`html
<div class="min-h-screen">
  <!-- Navigation -->
  <div class="navbar bg-base-100">
    <button class="btn btn-ghost" onclick="showPage('home')">Home</button>
    <button class="btn btn-ghost" onclick="showPage('about')">About</button>
  </div>

  <!-- Page 1 -->
  <div id="home" class="page-content">
    <div class="hero min-h-screen"><!-- home content --></div>
  </div>

  <!-- Page 2 -->
  <div id="about" class="page-content hidden">
    <div class="container mx-auto p-8"><!-- about content --></div>
  </div>

  <script>
    function showPage(pageId) {
      document.querySelectorAll('.page-content').forEach(el => el.classList.add('hidden'));
      document.getElementById(pageId).classList.remove('hidden');
    }
    // Initialize Lucide icons
    lucide.createIcons();
  </script>
</div>
\`\`\`
`;