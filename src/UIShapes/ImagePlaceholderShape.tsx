import { TLBaseShape, <PERSON><PERSON><PERSON><PERSON><PERSON>, H<PERSON><PERSON>ontainer, Rectangle2d } from 'tldraw';
'use client';

// Define the type for the image placeholder shape
export type ImagePlaceholderShape = TLBaseShape<
	'image-placeholder',
	{
		w: number;
		h: number;
	}
>;

// Create the ShapeUtil class for the image placeholder shape
export class ImagePlaceholderShapeUtil extends ShapeUtil<ImagePlaceholderShape> {
	static override type = 'image-placeholder' as const;

	getDefaultProps(): ImagePlaceholderShape['props'] {
		return {
			w: 150,
			h: 100,
		};
	}

	getGeometry(shape: ImagePlaceholderShape) {
		return new Rectangle2d({
			width: shape.props.w,
			height: shape.props.h,
			isFilled: true,
		});
	}

	component(shape: ImagePlaceholderShape) {
		const style: React.CSSProperties = {
			width: '100%',
			height: '100%',
			backgroundColor: '#e9e9e9', // Light grey background
			border: '1px dashed #aaa',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			color: '#aaa',
			fontSize: '14px',
			pointerEvents: 'none',
		};

		// Simple X to indicate image area
		const svgStyle: React.CSSProperties = {
			width: '80%',
			height: '80%',
			stroke: '#aaa',
			strokeWidth: 1,
		};

		return (
			<HTMLContainer id={shape.id}>
				<div style={style}>
					<svg style={svgStyle} viewBox="0 0 100 100" preserveAspectRatio="none">
						<line x1="0" y1="0" x2="100" y2="100" />
						<line x1="0" y1="100" x2="100" y2="0" />
					</svg>
				</div>
			</HTMLContainer>
		);
	}

	indicator(shape: ImagePlaceholderShape) {
		return <rect width={shape.props.w} height={shape.props.h} />;
	}
} 