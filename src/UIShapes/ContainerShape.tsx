import { TLBaseShape, <PERSON><PERSON><PERSON><PERSON>til, HTMLContainer, Rectangle2d } from 'tldraw';
'use client';

// Define the type for the container shape
export type ContainerShape = TLBaseShape<
	'container',
	{
		w: number;
		h: number;
		backgroundColor: string;
		title: string;
	}
>;

// Create the ShapeUtil class for the container shape
export class ContainerShapeUtil extends ShapeUtil<ContainerShape> {
	static override type = 'container' as const;

	getDefaultProps(): ContainerShape['props'] {
		return {
			w: 300,
			h: 200,
			backgroundColor: '#ffffff',
			title: '',
		};
	}

	getGeometry(shape: ContainerShape) {
		return new Rectangle2d({
			width: shape.props.w,
			height: shape.props.h,
			isFilled: true,
		});
	}

	component(shape: ContainerShape) {
		const containerStyle: React.CSSProperties = {
			width: '100%',
			height: '100%',
			backgroundColor: shape.props.backgroundColor || '#ffffff',
			border: '1px solid #cccccc',
			borderRadius: '4px',
			display: 'flex',
			flexDirection: 'column',
			pointerEvents: 'all', // Allow interaction with children if any
		};

		const titleStyle: React.CSSProperties = {
			padding: '4px 8px',
			fontSize: '12px',
			fontWeight: 'bold',
			color: '#555555',
			borderBottom: '1px solid #cccccc',
			textAlign: 'center',
			userSelect: 'none',
		};

		// Note: Actual children shapes are rendered separately by tldraw
		// This component just provides the visual container background and optional title
		return (
			<HTMLContainer id={shape.id}>
				<div style={containerStyle}>
					{shape.props.title && (
						<div style={titleStyle}>{shape.props.title}</div>
					)}
					{/* Children shapes are positioned relative to the canvas, not this div */}
				</div>
			</HTMLContainer>
		);
	}

	indicator(shape: ContainerShape) {
		return <rect width={shape.props.w} height={shape.props.h} />;
	}
} 