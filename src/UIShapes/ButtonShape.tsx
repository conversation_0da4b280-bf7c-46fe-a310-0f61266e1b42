import { TLBaseShape, <PERSON><PERSON><PERSON><PERSON><PERSON>, HTM<PERSON>ontainer, Rectangle2d } from 'tldraw';
'use client';

// Define the type for the button shape
export type ButtonShape = TLBaseShape<
	'button',
	{
		w: number;
		h: number;
		labelText: string;
		variant: 'primary' | 'secondary' | 'outline';
	}
>;

// Create the ShapeUtil class for the button shape
export class ButtonShapeUtil extends ShapeUtil<ButtonShape> {
	static override type = 'button' as const;

	getDefaultProps(): ButtonShape['props'] {
		return {
			w: 120,
			h: 40,
			labelText: 'Button',
			variant: 'primary',
		};
	}

	getGeometry(shape: ButtonShape) {
		return new Rectangle2d({
			width: shape.props.w,
			height: shape.props.h,
			isFilled: true,
		});
	}

	component(shape: ButtonShape) {
		const getVariantStyles = (variant: ButtonShape['props']['variant']) => {
			switch (variant) {
				case 'secondary':
					return { backgroundColor: '#6c757d', color: 'white', border: 'none' };
				case 'outline':
					return { backgroundColor: 'transparent', color: '#0d6efd', border: '1px solid #0d6efd' };
				case 'primary':
				default:
					return { backgroundColor: '#0d6efd', color: 'white', border: 'none' };
			}
		};

		const buttonStyle: React.CSSProperties = {
			width: '100%',
			height: '100%',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			fontSize: '14px',
			borderRadius: '4px',
			cursor: 'pointer',
			userSelect: 'none',
			pointerEvents: 'all', // Allow interactions like hover/click visuals if needed
			...getVariantStyles(shape.props.variant),
		};

		return (
			<HTMLContainer id={shape.id}>
				<button style={buttonStyle} disabled> {/* Disabled to prevent actual clicks during editing */}
					{shape.props.labelText}
				</button>
			</HTMLContainer>
		);
	}

	indicator(shape: ButtonShape) {
		return <rect width={shape.props.w} height={shape.props.h} />;
	}
} 