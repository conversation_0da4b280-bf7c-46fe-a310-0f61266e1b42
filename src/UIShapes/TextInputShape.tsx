import { TL<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Rectangle2d } from 'tldraw';
'use client';

// Define the type for the text input shape
export type TextInputShape = TLBaseShape<
	'text-input',
	{
		w: number;
		h: number;
		placeholderText: string;
		labelText?: string; // Optional label above the input
	}
>;

// Create the ShapeUtil class for the text input shape
export class TextInputShapeUtil extends ShapeUtil<TextInputShape> {
	static override type = 'text-input' as const;

	getDefaultProps(): TextInputShape['props'] {
		return {
			w: 200,
			h: 40,
			placeholderText: 'Enter text...',
			labelText: '', // Default to no label
		};
	}

	getGeometry(shape: TextInputShape) {
		// The geometry should encompass the label if it exists
		const labelHeight = shape.props.labelText ? 20 : 0; // Approximate height for the label
		return new Rectangle2d({
			width: shape.props.w,
			height: shape.props.h + labelHeight,
			isFilled: true,
		});
	}

	component(shape: TextInputShape) {
		const wrapperStyle: React.CSSProperties = {
			display: 'flex',
			flexDirection: 'column',
			width: '100%',
			height: '100%',
			pointerEvents: 'all', // Allow interaction with the input field
		};

		const labelStyle: React.CSSProperties = {
			fontSize: '12px',
			marginBottom: '4px',
			color: '#333',
			userSelect: 'none',
		};

		const inputStyle: React.CSSProperties = {
			width: '100%',
			height: `${shape.props.h}px`, // Use the height prop for the input itself
			padding: '8px',
			border: '1px solid #ccc',
			borderRadius: '4px',
			fontSize: '14px',
			boxSizing: 'border-box', // Ensure padding doesn't add to the height
		};

		return (
			<HTMLContainer id={shape.id}>
				<div style={wrapperStyle}>
					{shape.props.labelText && (
						<label style={labelStyle}>{shape.props.labelText}</label>
					)}
					<input
						style={inputStyle}
						type="text"
						placeholder={shape.props.placeholderText}
						disabled // Prevent interaction during editing
					/>
				</div>
			</HTMLContainer>
		);
	}

	indicator(shape: TextInputShape) {
		// Indicator should match the geometry (including potential label space)
		const labelHeight = shape.props.labelText ? 20 : 0;
		return <rect width={shape.props.w} height={shape.props.h + labelHeight} />;
	}
} 