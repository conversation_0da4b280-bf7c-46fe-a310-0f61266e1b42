import { TL<PERSON><PERSON>Shape, <PERSON><PERSON><PERSON><PERSON><PERSON>, HTM<PERSON>ontainer, Rectangle2d, T } from 'tldraw';
'use client';

// Define the type for the text label shape
export type TextLabelShape = TLBaseShape<
	'text-label',
	{
		w: number; // Width will likely be auto-calculated, but good to have
		h: number; // Height will likely be auto-calculated
		text: string;
		fontSize: number;
		fontWeight: 'normal' | 'bold' | 'lighter' | 'bolder' | number;
	}
>;

// Create the ShapeUtil class for the text label shape
export class TextLabelShapeUtil extends ShapeUtil<TextLabelShape> {
	static override type = 'text-label' as const;

	getDefaultProps(): TextLabelShape['props'] {
		return {
			w: 100, // Default width
			h: 20, // Default height
			text: 'Label Text',
			fontSize: 16,
			fontWeight: 'normal',
		};
	}

	// For text, geometry might be tricky. A simple bounding box is often sufficient.
	// More advanced: use text measurement APIs if precise bounds are needed.
	getGeometry(shape: TextLabelShape) {
		// Placeholder: Use props w/h. Ideally, this would measure the text.
		const width = shape.props.w > 0 ? shape.props.w : 100; // Fallback width
		const height = shape.props.h > 0 ? shape.props.h : 20; // Fallback height
		return new Rectangle2d({
			width: width,
			height: height,
			isFilled: false, // Text isn't typically 'filled' like a rectangle
		});
	}

	component(shape: TextLabelShape) {
		const labelStyle: React.CSSProperties = {
			width: '100%',
			height: '100%',
			display: 'flex',
			alignItems: 'center', // Center text vertically
			justifyContent: 'flex-start', // Align text to the start horizontally
			fontSize: `${shape.props.fontSize}px`,
			fontWeight: shape.props.fontWeight,
			color: '#333333',
			whiteSpace: 'nowrap', // Prevent wrapping for simple labels
			pointerEvents: 'none', // Labels are usually not interactive
			userSelect: 'none',
			// NOTE: For true auto-sizing, we'd need JS text measurement
			// This component relies on the parent container (HTMLContainer) size
		};

		return (
			<HTMLContainer id={shape.id}>
				<div style={labelStyle}>{shape.props.text}</div>
			</HTMLContainer>
		);
	}

	indicator(shape: TextLabelShape) {
		// Use the geometry's bounding box for the indicator
		const geo = this.getGeometry(shape);
		return <rect width={geo.w} height={geo.h} />;
	}

	// TODO: Implement resizing logic if needed, potentially involving text measurement.
	// Default resizing might just scale the bounding box, not reflow text.
} 