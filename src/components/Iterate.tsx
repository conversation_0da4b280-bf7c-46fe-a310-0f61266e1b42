'use client'
import { useState } from "react"
import { Textarea } from "./ui/textarea"
import { Button } from "./ui/button"
import { Info, Orbit, Sparkle, X } from "lucide-react"
import { useEditor, createShapeId, TLShapeId } from 'tldraw'
import { toast } from 'sonner'
import { HTMLDesignMockupsShape } from "@/shapes/HTMLDesignMockups"
import { HybridDesignMockupsShape } from "@/shapes/HybridDesignMockups"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
  } from "@/components/ui/popover"
  import { PopoverArrow, PopoverClose } from '@radix-ui/react-popover'

interface IterateProps {
    currentHtml: string;
}

export function Iterate({ currentHtml }: IterateProps) {
    const [input, setInput] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const editor = useEditor()

    //console.log('Iterate component rendered', { currentHtml, shapeId }) // Debug render

    const handleKeyDown = (e: React.KeyboardEvent) => {
        console.log('Key down event') // Debug keyboard
        e.stopPropagation()
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleIterate()
        }
    }

    const handleClick = (e: React.MouseEvent) => {
        console.log('Container clicked') // Debug container click
        e.stopPropagation()
    }

    const handleButtonClick = (e: React.MouseEvent) => {
        console.log('Button clicked') // Debug button click
        e.stopPropagation()
        e.preventDefault()
        handleIterate()
    }

    const handleIterate = async () => {
        console.log('handleIterate called', { input: input.trim(), editor: !!editor })
        if (!input.trim() || !editor) return

        setIsLoading(true)
        toast.info('Generating variation...')

        // Get current shape position and bounds
        // const currentShape = editor.getShape<HybridDesignMockupsShape>(shapeId)
        // const selectionBounds = editor.getSelectionRotatedPageBounds()
        // console.log('Current shape and bounds:', { currentShape, selectionBounds })
        // if (!currentShape || !selectionBounds) return

        // Create new shape slightly offset
        const newShapeId = createShapeId()
        const PADDING = 40 // Space between shapes

        try {
            console.log('Creating shape and sending API request...')
            // Create shape in loading state with position based on selection bounds
            editor.createShape({
                type: 'hybrid-design-mockups',
                id: newShapeId,
                x: 200 + PADDING, // Place to the right with padding
                y: 200, // Keep same vertical alignment
                props: {
                    html: '',
                    w: 1280, // Use same width as current shape
                    h: 800, // Use same height as current shape
                    loading: true
                },
            })

            console.log('Making API request with:', {
                currentHtml,
                iterationRequest: input
            })

            const response = await fetch('/api/ai-iteration', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    iterationRequest: `Current Design: ${currentHtml}\n\nIteration Request: ${input}\n\nPlease provide an updated version of this design that incorporates the requested changes. Return only the code.`,
                }),
            })

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()
            console.log('API Response:', data)

            if (data.HTMLDesigns) {
                editor.updateShape({
                    id: newShapeId,
                    type: 'hybrid-design-mockups',
                    props: {
                        html: data.HTMLDesigns,
                        loading: false
                    },
                })
                toast.success('Variation generated!')
            }

        } catch (error: any) {
            console.error('Error generating variation:', error)
            toast.error(`Failed to generate variation: ${error.message}`)

            // Clean up shape on error
            editor.deleteShape(newShapeId)
        } finally {
            setIsLoading(false)
            setInput('') // Clear input after completion
        }
    }

    return (
        <>
       
            <div
              
            className="flex flex-col w-[400px] max-w-[400px] min-h-[80px] max-h-[240px] p-8 overflow-hidden bg-[#f6f9fe] dark:bg-gray-800  absolute z-500 mr-[-400px] mt-[20px] right-0 rounded-tr-2xl rounded-br-2xl border-white border-2" onPointerDown={(e) => e.stopPropagation()} style={{ pointerEvents: 'all', }}>
            
              {/* <PopoverArrow className="h-4 w-8 text-white" /> */}
              
              <div>
            <h2 className="text-lg font-bold mb-2">Create a variation (beta)</h2>
            <Textarea
                value={input}
                onChange={(e) => {
                    console.log('Textarea changed') // Debug textarea
                    e.stopPropagation()
                    setInput(e.target.value)
                }}
                onKeyDown={handleKeyDown}
                placeholder="Remove the background, add a gradient, change the font..."
                className="flex-grow border-none shadow-none resize-none min-h-[60px] max-h-[80px] bg-[#e4eeff] dark:bg-gray-800"
            />
            <div className="flex justify-end mt-2">
                <Button
                    variant="outline"
                    onClick={handleButtonClick}
                    disabled={isLoading}
                    type="button"
                >
                    {isLoading ? 'Generating...' : 'Generate'}
                </Button>
            </div>
        </div>
              </div>
        {/* <div
            className="flex flex-col w-[400px] max-w-[400px] min-h-[80px] max-h-[240px] p-4 overflow-hidden rounded-lg shadow-lg bg-white dark:bg-gray-800"
            style={{ width: 'clamp(300px, 90vw, 600px)', pointerEvents: 'all' }}
            onClick={handleClick}
        >
            <h2 className="text-lg font-bold mb-2">Create a variation</h2>
            <Textarea
                value={input}
                onChange={(e) => {
                    console.log('Textarea changed') // Debug textarea
                    e.stopPropagation()
                    setInput(e.target.value)
                }}
                onKeyDown={handleKeyDown}
                placeholder="Remove the background, add a gradient, change the font..."
                className="flex-grow border-none shadow-none resize-none min-h-[60px] max-h-[60px] bg-gray-100 dark:bg-gray-700"
            />
            <div className="flex justify-end mt-2">
                <Button
                    variant="outline"
                    onClick={handleButtonClick}
                    disabled={isLoading}
                    type="button"
                >
                    {isLoading ? (
                        <Orbit className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Sparkle className="w-4 h-4 mr-2 text-[#e70076]" />
                    )}
                    {isLoading ? 'Generating...' : 'Generate'}
                </Button>
            </div>
        </div> */}
        </>
    );
}