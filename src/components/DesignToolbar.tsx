'use client';
import { Tldraw, createShapeId, TLUiComponents, TLEditorComponents, track, useEditor  } from 'tldraw'
import { useState, useCallback, useEffect } from 'react';
import { Button } from "./ui/button";
import { Figma, GraduationCap, Rotate3D, Smartphone } from 'lucide-react'
import ReactMarkdown from 'react-markdown';
// Remove static imports for browser-only libraries
import { documentToSVG, elementToSVG, inlineResources } from 'dom2svg'
import * as htmlToImage from 'html-to-image';
import { toPng, toJpeg, toBlob, toPixelData, toSvg } from 'html-to-image';
import * as domtoimage from "dom-to-image";
import htmlToSvg from "htmlsvg";
import { Iterate } from './Iterate';


interface DesignToolbarProps {
    ResearchData: any;
    htmlData: any;
    id: string;
}

export function DesignToolbar({ResearchData, htmlData, id}: DesignToolbarProps) {
    const editor = useEditor()
    const [isResearchVisible, setIsResearchVisible] = useState(false);
    const [isIterateVisible, setIsIterateVisible] = useState(false);
    const html = htmlData; 
    const researchData = ResearchData ;


    const handleCopyToClipboardForFigma = useCallback(async (id: string) => {
        // Only run in browser
        if (typeof window === 'undefined') return;

        console.log('Copying to clipboard...');
        if (!id) {
            console.error('No id provided');
            return;
        }

        const mockUpElement = document.getElementById(`${id}`);
        //const svgDocument = documentToSVG(document)
        //const mockUpElement = window.document.body.querySelector(`#${id}`);
        if (!mockUpElement) {
            console.error('Element not found');
            return;
        }
        try {
function getComputedStyles(element: HTMLElement) {
  const computedStyle = window.getComputedStyle(element);
  const styles: { [key: string]: string } = {};

  // Iterate over all computed properties
  for (let i = 0; i < computedStyle.length; i++) {
    const propertyName = computedStyle[i];
    const propertyValue = computedStyle.getPropertyValue(propertyName);
    styles[propertyName] = propertyValue;
  }
  return styles;
}
        const elementStyles = getComputedStyles(mockUpElement);
        console.log('computedStyle: ', elementStyles);
            // Dynamically import browser-only libraries

            const svg = await htmlToSvg(mockUpElement);
console.log('htmlsvg: ', svg);
            const { elementToSVG, inlineResources } = await import('dom2svg');

            const svgDocument = await elementToSVG(mockUpElement);
            console.log('svgDocument: ', svgDocument);
            //await inlineResources(svgDocument.documentElement);
            const svgString = new XMLSerializer().serializeToString(svgDocument);
            navigator.clipboard.writeText(svgString);
// htmlToImage
//     .toSvg(mockUpElement)
//     .then(function (dataUrl) {
//         const link = document.createElement('a')
//         link.download = 'my-image-name.svg'
//         link.href = dataUrl
//         link.click()
//     })

        } catch (error) {
            console.error('Error copying to clipboard:', error);
        }
    } , [id]);

    return (
        <>
            <div id="designToolbar"
                className='flex flex-row justify-between h-10 w-full mt-[-50px] items-center z-1500 relative' style={{ pointerEvents: 'all', }}>
                <Button
                    variant="outline"
                    className="rounded-full"
                    onClick={() => setIsResearchVisible(!isResearchVisible)}
                    onPointerDown={(e) => e.stopPropagation()}
                >
                    <GraduationCap size={20} />{isResearchVisible ? 'Hide Research' : 'View Research'}
                </Button>
                <Button variant="outline" className="rounded-full"
                    onPointerDown={(e) => e.stopPropagation()}><SmartphonePhone size={20} /></Button>
                <Button variant="outline" className="rounded-full" onClick={() => handleCopyToClipboardForFigma(id)} onPointerDown={(e) => e.stopPropagation()}><Figma size={20} />Copy to Figma</Button>
                <Button variant="outline" className="rounded-full" onClick={() => setIsIterateVisible(!isIterateVisible)}
                    onPointerDown={(e) => e.stopPropagation()}><Rotate3D size={20} />Ideate</Button>
            </div>
            {isIterateVisible && (
                <Iterate currentHtml={html} />
            )}
            {isResearchVisible && (
                <div id="Research" className="flex flex-col text-normal h-auto w-[500px] bg-[#f6f9fe] relative z-500 ml-[-500px] mt-[20px] leading-relaxed p-8 whitespace-break-spaces rounded-tl-2xl border-white border-2">
                    <ReactMarkdown>{researchData}</ReactMarkdown>
                </div>
            )}
        </>

    );
}
