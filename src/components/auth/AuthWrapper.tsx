'use client'

import { useAuth } from '@/lib/auth'
import { AuthForm } from '@/components/auth/AuthForm'
import { UserProfile } from '@/components/auth/UserProfile'
import { Loader2 } from 'lucide-react'

interface AuthWrapperProps {
  children: React.ReactNode
  requireAuth?: boolean
}

export function AuthWrapper({ children, requireAuth = false }: AuthWrapperProps) {
  const { user, loading } = useAuth()

  // If we're still loading, show a loading indicator
  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-10 w-10 animate-spin text-primary" />
      </div>
    )
  }

  // If auth is required and user is not logged in, show auth form
  if (requireAuth && !user) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
        <AuthForm />
      </div>
    )
  }

  // If user is logged in or auth is not required, show the content
  // For authenticated users, we also include the UserProfile component
  return (
    <>
      {user && (
        <div className="fixed top-4 right-4 z-50">
          <UserProfile />
        </div>
      )}
      {!user && !requireAuth && (
        <div className="fixed top-4 right-4 z-50">
          <AuthForm />
        </div>
      )}
      {children}
    </>
  )
} 