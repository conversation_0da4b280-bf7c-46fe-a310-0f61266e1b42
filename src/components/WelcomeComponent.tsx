import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Spark<PERSON> } from "lucide-react";
import { HighlightText } from './animate-ui/text/highlight'
import Image from "next/image";
import { ChevronRight } from "lucide-react";


export function WelcomeComponent() {
  return (
    <div className="flex flex-row w-auto h-auto overflow-hidden bg-transparent dark:bg-white border border-gray-200 dark:border-gray-700"
      style={{
        position: 'absolute',
        top: (window.innerHeight - 500) / 2,
        left: (window.innerWidth - 1000) / 2,
      }}>
      <div className="flex flex-col items-center gap-2 min-w-[300px]">
        <img draggable="false" src="/UXA_Logo4.png" alt="Logo" width={300} height={300} className="mx-auto" />
        <span className="text-xl md:text-1xl font-medium">Discover</span>
        <h1 className="text-4xl font-bold">UXJoe</h1>


      </div>
      <div className="flex flex-col gap-4 mx-16 min-w-[600px] mt-8 ml-24">
        <span className="text-2xl font-light">Visualization and Ideation</span>
        <span className="text-xl font-medium">Informed by UX Insights</span>
        <div className="flex flex-col gap-4 my-4">
          <Button
            className="bg-white w-auto h-auto p-2 whitespace-break-spaces shadow-lg rounded-2xl text-left"
            variant="outline"
            size="sm"
          >
            Design a mobile product detail page for an e-commerce app showing images, description, price, and an "Add to Cart" button
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
          <Button
            className="bg-white w-auto h-auto p-2 whitespace-break-spaces shadow-lg rounded-2xl text-left"
            variant="outline"
            size="sm"
          >
            Create a 3-step mobile onboarding flow: Welcome, Permissions Request, and Profile Setup
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
          <Button
            className="bg-white w-auto h-auto p-2 whitespace-break-spaces shadow-lg rounded-2xl text-left"
            variant="outline"
            size="sm"
          >
            Create a well designed Search Results native app view of Apartments for rent. Make sure you include a "Check Availability" button for each listing. Design it like it was made by Airbnb. The user's goal is to find an apartment to rent. The business goal is to increase conversions.
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* <p className="mb-4 leading-relaxed">
                      Welcome! This tool is your AI co-pilot designed to accelerate your UX design process.
                      Simply describe the UI screen or flow you need. Your assistant will:
                    </p>
                    <ul className="list-disc list-inside mb-4 space-y-1 text-sm">
                      <li>Consider your request and relevant UX best practices (we do the research for you!).</li>
                      <li>Provide best practice research and generate Visual Mockups of the UI.</li>
                    </ul>
                    <p className="leading-relaxed">
                      Use this assistant to quickly ideate and visualize concepts all informed by UX insights.
                    </p>

                    <Separator className="my-6 bg-gray-400 h-[1px]" />

                    <h3 className="text-md font-semibold text-white mb-3">Try These Example Prompts:</h3>

                    <h4 className="text-sm font-semibold text-white mb-3">Simple Prompts(15-30secs to generate):</h4>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Design a mobile product detail page for an e-commerce app showing images, description, price, and an "Add to Cart" button"</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Create a 3-step mobile onboarding flow: Welcome, Permissions Request, and Profile Setup"</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Create a well designed Search Results native app view of Apartments for rent. Make sure you include a "Check Availability" button for each listing. Design it like it was made by Airbnb. The user's goal is to find an apartment to rent. The business goal is to increase conversions."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                        >
                          Try it
                        </Button>
                      </li>
                    </ul>

                    <h4 className="text-sm font-semibold text-white mb-3">More Complex Prompts(20-40secs to generate):</h4>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"I need a really great UX Portfolio site that will impress FANG companies and get me hired. Design 4 common portfolio pages with all the details and content added. Make it look really really good please. Ultra Modern style and dark mode."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"I'm trying to determine the best way to display a summarization view within an AI conversation chat view. The feature periodically generates a summarization of the conversation. Not sure whats the best interaction and placement for the user to access it. I'll need to see the conversation with and without the summarization view opened. Create 3 variations of approaches."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                        >
                          Try it
                        </Button>
                      </li>
                    </ul>

                    <Separator className="my-6 bg-gray-400 h-[1px]" />

                    <h3 className="text-md font-semibold text-white mb-3">Work in Progress:</h3>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span>• Faster output</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• History of Prompts</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Import/Export to Figma</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Refine/Revisions to generated designs with AI</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Edit designs directly on the canvas</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Upload Designs in your prompts</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Desktop Mockups</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Get existing user research/insight from the web</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Design Inspiration</span>
                      </li>

                      <li className="flex items-center justify-between gap-2">
                        <span>• Saving and storage</span>
                      </li>
                    </ul> */}
    </div>
  );
}
