'use client'

import {
    Popover,
    PopoverContent,
    PopoverTrigger,
  } from "@/components/ui/popover"
  import { PopoverArrow, PopoverClose } from '@radix-ui/react-popover'
import { Info, X, Clock } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Button } from "./ui/button";
import { usePromptHistory, PromptHistoryItem } from "@/hooks/usePromptHistory"
import { useRef } from "react"

interface HistoryProps {
  onSelectPrompt: (prompt: string) => void;
}

export function History({ onSelectPrompt }: HistoryProps) {
  const { getPromptHistory } = usePromptHistory()
  const closeButtonRef = useRef<HTMLButtonElement>(null)
  const history = getPromptHistory()

  const handlePromptSelect = (prompt: string) => {
    onSelectPrompt(prompt)
    closeButtonRef.current?.click()
  }

  const formatDate = (timestamp: number) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(new Date(timestamp))
  }

  return (
    <div>
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="h-10 border-none hover:bg-gray-800 hover:text-white">
            <Clock className="w-4 h-4" /><span className="hidden sm:inline ml-2">History</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="flex flex-col w-[95vw] max-w-3xl h-[70vh] md:h-[80vh] lg:h-[60vh] p-0 overflow-hidden bg-gray-800 dark:bg-white border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
          style={{ width: 'clamp(300px, 90vw, 600px)' }}
          sideOffset={8}
          align="center"
          side="top"
        >
          <PopoverArrow className="h-4 w-8 text-white" />
          <div className="px-4 pt-4 relative h-[40px]">
            <PopoverClose ref={closeButtonRef} asChild>
              <Button variant="outline" className="absolute right-2 top-2 w-8 h-8 bg-transparent hover:bg-black">
                <X className="h-4 w-4 text-white" />
              </Button>
            </PopoverClose>
          </div>

          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full w-full px-4 pb-8 text-white">
              <div className="space-y-4 max-w-2xl mx-auto">
                {history.length === 0 ? (
                  <div className="text-center text-gray-400 mt-8">
                    No prompt history yet
                  </div>
                ) : (
                  <div className="space-y-2">
                    {history.map((item: PromptHistoryItem, index: number) => (
                      <div key={item.timestamp} className="group">
                        <button
                          onClick={() => handlePromptSelect(item.prompt)}
                          className="w-full text-left p-3 rounded-lg hover:bg-gray-700 transition-colors"
                        >
                          <p className="text-sm text-gray-300 mb-1">
                            {formatDate(item.timestamp)}
                          </p>
                          <p className="text-white line-clamp-2">
                            {item.prompt}
                          </p>
                        </button>
                        {index < history.length - 1 && (
                          <Separator className="my-2 bg-gray-700" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}