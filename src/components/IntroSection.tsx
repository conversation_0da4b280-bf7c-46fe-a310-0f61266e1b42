// src/components/ui/IntroSection.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Assuming Shadcn 
import { stopEventPropagation, Tldraw, TLEditorComponents, track, useEditor } from 'tldraw'

// Card is here
import { Separator } from "@/components/ui/separator"; // Assuming Shadcn Separator is here
import { Sparkles } from 'lucide-react'; // Assuming Lucid icons are installed and available

const IntroSection: React.FC = () => {
  return (
    // Using a div as a wrapper that you can position on your canvas
    // Add appropriate positioning/margin classes based on your canvas layout
    <div className="p-4 rounded-lg shadow-md inline-block" style={{
        position: 'absolute',
        top: 50,
        left: 50,
        //width: 200,
        padding: 12,
        borderRadius: 8,
        backgroundColor: 'goldenrod',
        zIndex: 0,
        userSelect: 'text',
        boxShadow: '0 0 0 1px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.1)',
    }}
    onPointerDown={stopEventPropagation}
    onPointerMove={stopEventPropagation}> {/* Example container styling */}
      <Card className="shadow-lg w-[500px] h-full p-4"> {/* Use Shadcn Card for container */}
        <CardHeader className="flex flex-row items-center space-x-3 p-4 pb-2"> {/* Header with icon */}
          <Sparkles className="w-6 h-6 text-blue-500" /> {/* Lucid Icon */}
          <CardTitle className="text-xl font-bold text-gray-800">
            Your AI-Powered UX Design Assistant
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0 text-gray-700 text-sm"> {/* Content area */}
          <p className="mb-4 leading-relaxed">
            Welcome! This tool is your AI co-pilot designed to accelerate your UX design process.
            Simply describe the UI screen or flow you need in the chat box. Your assistant will:
          </p>
          <ul className="list-disc list-inside mb-4 space-y-1 text-sm"> {/* List of steps */}
            <li>Consider your request and relevant UX best practices (we do the research for you!).</li>
            <li>Architect a detailed design specification.</li>
            <li>Generate ready-to-visualize JSX code mockups based on the specification.</li>
          </ul>
          <p className="leading-relaxed">
            Use this assistant to quickly ideate, visualize concepts, and get initial design blueprints, all informed by UX insights.
          </p>

          <Separator className="my-6" /> {/* Separator line */}

          <h3 className="text-md font-semibold text-gray-800 mb-3">Try These Example Prompts:</h3> {/* Subheading for examples */}

          <ul className="list-disc list-inside space-y-2 text-sm"> {/* List of example prompts */}
            <li>Design a mobile login screen with email and password fields and a "Forgot Password" link.</li>
            <li>Create a 3-step mobile onboarding flow: Welcome, Permissions Request, and Profile Setup.</li>
            <li>Design a mobile product detail page for an e-commerce app showing images, description, price, and an "Add to Cart" button.</li>
            <li>Design a mobile dashboard view for a fitness tracker app showing daily steps, calories burned, and active minutes in a card layout.</li>
            <li>Generate a mobile settings screen with options for notifications, account privacy, and help/support links.</li>
            <li>Design a mobile task list view with checkboxes, task titles, and a button to add new tasks.</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntroSection;