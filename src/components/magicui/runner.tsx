import { Runner, importCode } from 'react-runner'
import * as Button from '@/components/ui/button'
import * as Input from '@/components/ui/input'
import * as Textarea from '@/components/ui/textarea'
import * as Label from '@/components/ui/label'
import * as Checkbox from '@/components/ui/checkbox'
import * as Card from '@/components/ui/card'
import * as Alert from '@/components/ui/alert'
import * as Badge from '@/components/ui/badge'
import * as Avatar from '@/components/ui/avatar'
import * as Separator from '@/components/ui/separator'
import * as Switch from '@/components/ui/switch'
import * as Skeleton from '@/components/ui/skeleton'
import * as Progress from '@/components/ui/progress'
import * as Select from '@/components/ui/select'
import * as Tabs from '@/components/ui/tabs'
import * as Toggle from '@/components/ui/toggle'
import * as Accordion from '@/components/ui/accordion'
import * as Menubar from '@/components/ui/menubar'
import * as Calendar from '@/components/ui/calendar'
import * as Command from '@/components/ui/command'
import * as RadioGroup from '@/components/ui/radio-group'
import * as Slider from '@/components/ui/slider'
import * as Breadcrumb from '@/components/ui/breadcrumb'
import * as Carousel from '@/components/ui/carousel'
import * as Pagination from '@/components/ui/pagination'
import * as ScrollArea from '@/components/ui/scroll-area'
import * as Table from '@/components/ui/table'
import * as lucideIcons from 'lucide-react';


const scope = {
    // scope used by import statement
    import: {
        '@/components/ui/button': Button,
        '@/components/ui/input': Input,
        '@/components/ui/textarea': Textarea,
        '@/components/ui/label': Label,
        '@/components/ui/checkbox': Checkbox,
        '@/components/ui/card': Card,
        '@/components/ui/alert': Alert,
        '@/components/ui/badge': Badge,
        '@/components/ui/avatar': Avatar,
        '@/components/ui/separator': Separator,
        '@/components/ui/switch': Switch,
        '@/components/ui/skeleton': Skeleton,
        '@/components/ui/progress': Progress,
        '@/components/ui/select': Select,
        '@/components/ui/tabs': Tabs,
        '@/components/ui/toggle': Toggle,
        '@/components/ui/accordion': Accordion,
        '@/components/ui/menubar': Menubar,
        '@/components/ui/calendar': Calendar,
        '@/components/ui/command': Command,
        '@/components/ui/radio-group': RadioGroup,
        '@/components/ui/slider': Slider,
        '@/components/ui/breadcrumb': Breadcrumb,
        '@/components/ui/carousel': Carousel,
        '@/components/ui/pagination': Pagination,
        '@/components/ui/scroll-area': ScrollArea,
        '@/components/ui/table': Table,
        'lucide-react': lucideIcons,
        
    },
}

const code = `
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from "@/components/ui/select"
import { 
  Tabs, TabsContent, TabsList, TabsTrigger
} from "@/components/ui/tabs"
import { Toggle } from "@/components/ui/toggle"
import { 
  Accordion, AccordionContent, AccordionItem, AccordionTrigger 
} from "@/components/ui/accordion"
import { 
  Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarTrigger 
} from "@/components/ui/menubar"
import { Calendar } from "@/components/ui/calendar"
import { 
  Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList 
} from "@/components/ui/command"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { 
  Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"
import { 
  Carousel, CarouselContent, CarouselItem 
} from "@/components/ui/carousel"
import { 
  Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious 
} from "@/components/ui/pagination"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { 
  Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"
 import * as Lucide from 'lucide-react'; 

export default function App() {

  return (
  <>
  
<Card className="bg-white rounded-lg shadow-sm p-4">
  <h2 className="text-lg font-semibold text-gray-800 mb-3">Your Coverage</h2>
  <div className="flex items-center justify-between text-sm text-gray-700 mb-2">
    <span>Plan Name:</span>
    <span className="font-medium">Gold PPO 1000</span>
  </div>
   <div className="flex items-center justify-between text-sm text-gray-700 mb-2">
    <span>Member ID:</span>
    <span className="font-medium">XYZ123456789</span>
  </div>
   <div className="flex items-center justify-between text-sm text-gray-700">
    <span>Effective Date:</span>
    <span className="font-medium">01/01/2024</span>
  </div>
  <Button variant="link" className="p-0 mt-3 h-auto text-blue-600 text-sm">View Full Benefits</Button>
</Card>


</>
  )
}
`



export default function RunnerComponent() {
    return <div className="absolute bottom-0 right-0 w-[1200px] h-[700px] bg-red-500 overflow-scroll " style={{ zIndex: 5000 }}><Runner code={code} scope={scope} /></div>
}