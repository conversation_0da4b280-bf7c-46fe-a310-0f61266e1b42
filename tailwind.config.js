/** @type {import('tailwindcss').Config} */

module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  safelist: [
    'mobilescreen', // Safelist the class you defined
    // Add patterns for classes the AI is likely to generate:
    {
      pattern: /bg-(red|green|blue|gray|yellow|purple|pink|white|black)-(100|200|300|400|500|600|700|800|900)/, // Common background colors
    },
    {
      pattern: /bg-(red|green|blue|gray|yellow|purple|pink|white|black)-(10|20|30|40|50|60|70|80|90)/, // Common background colors
    },
    {
      pattern: /text-(red|green|blue|gray|yellow|purple|pink|white|black)-(100|200|300|400|500|600|700|800|900)/, // Common text colors
    },
     {
      pattern: /border-(red|green|blue|gray|yellow|purple|pink)-(100|200|300|400|500|600|700|800|900)/, // Common border colors
    },
    {
      pattern: /(m|p)(t|b|l|r|x|y)?-(0|1|2|3|4|5|6|8|10|12|16|20|24|32|40|48|56|64)/, // Common margins/paddings
    },
    {
      pattern: /w-(full|screen|px|1|2|3|4|5|6|8|10|12|16|20|24|32|40|48|56|64|\d+\/\d+)/, // Common widths (incl fractions)
    },
     {
      pattern: /h-(full|screen|px|1|2|3|4|5|6|8|10|12|16|20|24|32|40|48|56|64)/, // Common heights
    },
    { pattern: /bg-(red|green|blue|yellow|indigo|purple|pink|gray|zinc|neutral|stone)-(100|200|300|400|500|600|700|800|900)/ },
    { pattern: /text-(red|green|blue|yellow|indigo|purple|pink|gray|zinc|neutral|stone)-(100|200|300|400|500|600|700|800|900)/ },
    { pattern: /border-(red|green|blue|yellow|indigo|purple|pink|gray|zinc|neutral|stone)-(100|200|300|400|500|600|700|800|900)/ },
    { pattern: /ring-(red|green|blue|yellow|indigo|purple|pink|gray|zinc|neutral|stone)-(100|200|300|400|500|600|700|800|900)/ },
    { pattern: /p[xy]?-(0|1|2|3|4|5|6|8|10|12|16|20|24|32)/ }, // Common padding values
    { pattern: /m[xy]?-(0|1|2|3|4|5|6|8|10|12|16|20|24|32|auto)/ }, // Common margin values
    { pattern: /gap-x?-(0|1|2|3|4|5|6|8|10|12|16)/ }, // Common gap values
    { pattern: /gap-y?-(0|1|2|3|4|5|6|8|10|12|16)/ },
    { pattern: /space-x?-(0|1|2|3|4|5|6|8|10|12|16)/ }, // Common space values
    { pattern: /space-y?-(0|1|2|3|4|5|6|8|10|12|16)/ },
    { pattern: /w-(auto|\d+\/?\d*|px|full|screen|min|max|fit)/ }, // Common widths
    { pattern: /h-(auto|\d+\/?\d*|px|full|screen|min|max|fit)/ }, // Common heights
    { pattern: /max-w-(xs|sm|md|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|full|min|max|prose|none)/ }, // Max widths
    { pattern: /flex/ },
    { pattern: /grid/ },
    { pattern: /block/ },
    { pattern: /inline-block/ },
    { pattern: /inline/ },
    { pattern: /hidden/ },
    { pattern: /items-(start|end|center|baseline|stretch)/ },
    { pattern: /justify-(start|end|center|between|around|evenly)/ },
    { pattern: /rounded-(none|sm|md|lg|xl|2xl|3xl|full)/ },
    { pattern: /shadow-(sm|md|lg|xl|2xl|inner|none)/ },
    { pattern: /text-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl)/ }, // Text sizes
    { pattern: /font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)/ }, // Font weights
  ],
  darkMode: "class",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          DEFAULT: "var(--primary)",
          foreground: "var(--primary-foreground)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          foreground: "var(--secondary-foreground)",
        },
        destructive: {
          DEFAULT: "var(--destructive)",
          foreground: "var(--destructive-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        accent: {
          DEFAULT: "var(--accent)",
          foreground: "var(--accent-foreground)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
        sidebar: {
          DEFAULT: "var(--sidebar)",
          foreground: "var(--sidebar-foreground)",
          primary: {
            DEFAULT: "var(--sidebar-primary)",
            foreground: "var(--sidebar-primary-foreground)",
          },
          accent: {
            DEFAULT: "var(--sidebar-accent)",
            foreground: "var(--sidebar-accent-foreground)",
          },
          border: "var(--sidebar-border)",
          ring: "var(--sidebar-ring)",
        },
        chart: {
          1: "var(--chart-1)",
          2: "var(--chart-2)",
          3: "var(--chart-3)",
          4: "var(--chart-4)",
          5: "var(--chart-5)",
        },
      },
      borderRadius: {
        lg: "var(--radius-lg)",
        md: "var(--radius-md)",
        sm: "var(--radius-sm)",
        xl: "var(--radius-xl)",
      },
      fontFamily: {
        sans: ["var(--font-geist-sans)"],
        mono: ["var(--font-geist-mono)"],
      },
    },
  },
  
};

export default config; 