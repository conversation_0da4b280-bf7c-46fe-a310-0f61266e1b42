# tldraw ai module
## Installation

For production, install the `@tldraw/ai` package in a new repository, such as a fork of tldraw's [Vite template](https://github.com/tldraw/vite-template). See the [tldraw repository](https://github.com/tldraw/tldraw) for more resources.

Install the `@tldraw/ai` package from NPM or your package manager of choice.

```bash
npm i @tldraw/ai
```

## Usage

Use the `useTldrawAi` hook in your code.

```tsx
function App() {
	return (
		<div style={{ position: 'fixed', inset: 0 }}>
			<Tldraw persistenceKey="example">
				<MyPromptUi />
			</Tldraw>
		</div>
	)
}

const TLDRAW_AI_OPTIONS = {
	transforms: [],
	generate: async ({ editor, prompt, signal }) => {
		// todo, return changes
		return []
	},
	stream: async function* ({ editor, prompt, signal }) {
		// todo, yield each change as it is ready
	},
}

function MyPromptUi() {
	const ai = useTldrawAi(TLDRAW_AI_OPTIONS)

	return (
		<div style={{ position: 'fixed', bottom: 100, left: 16 }}>
			<button onClick={() => ai.prompt('draw a unicorn')}>Unicorn</button>
		</div>
	)
}
```

Read on for more tips about using and configuring the hook.

## Guide

### Changes

The fundamental unit in tldraw's ai module is the "change". A change is an instruction to the tldraw editor to do one of the following:

- `createShape` creates a shape
- `updateShape` updates a shape
- `deleteShape` deletes a shape
- `createBinding` creates a binding
- `updateBinding` updates a binding
- `deleteBinding` deletes a binding

See `package/src/types.ts` for more about each change.

Changes should be generated by the `generate` or `stream` methods of the `useTldrawAi` configuration. You can do generate the changes using whichever method you wish, however the expectation is that you will send information to an LLM or other model to generate changes for you.

You may find that models are bad at generating changes directly. In our example project, we communicate with the LLM using a "simplified" format, parsing the response before sending back the actual changes expected by the ai module.

### Transforms

The ai module has support for "transforms" (`TldrawAiTransform`). This feature allows you to modify the user's prompt (the data extracted from the canvas) and then use those modifications again later when handling changes. These are useful when preparing the data into a format that is easier for an LLM to work with.

Our example project includes several of these. When extracting data from the canvas, `SimpleIds` transform replaces tldraw's regular ids (which look like `shape:some_long_uuid`) with simplified ids (like `1` or `2`). Later, when handling changes, the transform replaces the simplified ids with their original ids (or creates new ones).

To create a transform, extend the `TldrawAiTransform` class:

```ts
export class MyCustomTransform extends TldrawAiTransform {
	override transformPrompt = (prompt: TLAiPrompt) => {
		// modify the prompt when it's first created
	}

	override transformChange = (change: TLAiChange) => {
		// modify each change when it's being handled
	}
}
```

Pass your new class to the `useTldrawAi` hook's configuration.

```ts
const MY_STATIC_CONFIG: TldrawAiOptions = {
	transforms: [MyCustomTransform],
	...etc,
}
```

When the user creates a new prompt, the ai module will create a new instance of each transform to be used for that prompt only. This means that you can stash whatever data you wish on the instance. See the examples in `example/client/transforms` as a reference.

### The hooks

#### `TldrawAiModule`

The `TldrawAiModule` class is responsible for

1. Getting information about the current tldraw editor's canvas
2. Incorporating transforms before and after changes are generated
3. Applying "changes" to the tldraw editor's canvas

#### `useTldrawAiModule`

The package exports a hook, `useTldrawAiModule`, that creates an instance of the `TldrawAiModule` class for you to use in React. This class handles tasks such as getting information out of the tldraw canvas and applying changes to the tldraw canvas.

#### `useTldrawAi`

The `useTldrawAi` hook adds an extra layer of convenience around the ai module. This hook handles many of the standard behaviors for you. While we expect to expand this hook to support more configration, you may find it necessary to create your own version of this hook (based on its source code) in order to customize it further.

The hook responds with three methods: `prompt`, `repeat`, and `cancel`.

- `prompt` accepts either a string or a configuration object with `messages` and `stream`. By default, the `prompt` method will call your configuration's `generate` method. If `stream` is true, then it will call your configuration's `stream` method.
- `cancel` will cancel any currently running generation.
- `repeat` will apply the same changes that were generated last time. This is useful for debugging.

**Generate vs. Stream**

You don't need to define both `generate` and `stream`, though you should define one of them. If you call `ai.prompt` with the `stream` flag set to true, but don't have `stream` implemented, then you'll get an error; likewise, if you call `ai.prompt` without the `stream` flag and without `generate`, then you'll get an error. Just be sure to implement one or both.

**Static configuration**

If you're using the `useTldrawAi` hook, we recommend creating a custom hook that passes static options to the `useTldrawAi` hook. See the `useTldrawAiExample` hook in our example project as a reference.

```ts
export function useMyCustomAiHook() {
	const ai = useTldrawAi(MY_STATIC_OPTIONS)
}

const MY_STATIC_OPTIONS: TldrawAiOptions = {
	transforms: [],
	generate: async ({ editor, prompt, signal }) => {
		// todo, return changes
		return []
	},
	stream: async function* ({ editor, prompt, signal }) {
		// todo, yield each change as it is ready
	},
}
```

If you _must_ define the options inside of a React component, it's important that you memoize the options correctly.

```tsx
export function MyPromptUi() {
	const myMemoizedOptions = useMemo<TldrawAiOptions>(() => {
		return {
			transforms: [],
			generate: async ({ editor, prompt, signal }) => {
				return []
			},
			stream: async function* ({ editor, prompt, signal }) {},
		}
	}, [])

	const ai = useTldrawAi(myMemoizedOptions)

	return <etc />
}
```

**Calling the hook**

The ai module relies on the tldraw editor at runtime. You should use the `useTldrawAi` hook inside of the tldraw editor's React context, or else provide it with the current `editor` instance as a prop.

You can do that via a child component:

```tsx
function App() {
	return (
		<div style={{ position: 'fixed', inset: 0 }}>
			<Tldraw persistenceKey="example">
				<MyPromptUi />
			</Tldraw>
		</div>
	)
}

function MyPromptUi() {
	const ai = useMyCustomAiHook()
	return (
		<div style={{ position: 'fixed', bottom: 100, left: 16 }}>
			<button onClick={() => ai.prompt('draw a unicorn')}>Unicorn</button>
		</div>
	)
}
```

Or via the Tldraw component's `components` prop:

```tsx
const components: TLComponents = {
	InFrontOfTheCanvas: () => {
		const ai = useMyCustomAiHook()
		return (
			<div>
				<button onClick={() => ai.prompt('draw a unicorn')}>Unicorn</button>
			</div>
		)
	},
}

function App() {
	return (
		<div style={{ position: 'fixed', inset: 0 }}>
			<Tldraw persistenceKey="example" components={components} />
		</div>
	)
}
```

If this is inconvenient—or if you like a challenge—you can also pass the `editor` as an argument to `useTldrawAi`. While this involves some "juggling", it may be useful when you wish to place the ai module into a global context or necessary if you need to use it in different parts of your document tree.

```tsx
function App() {
	const [editor, setEditor] = useState<Editor | null>(null)

	return (
		<div style={{ position: 'fixed', inset: 0 }}>
			<TldrawBranch onEditorMount={setEditor} />
			{editor && <MyPromptUi editor={editor} />}
		</div>
	)
}

function TldrawBranch({ onMount }: { onMount: (editor: Editor) => void }) {
	return (
		<div style={{ position: 'fixed', inset: 0 }}>
			<Tldraw persistenceKey="example" onMount={onEditorMount} />
		</div>
	)
}

function MyPromptUi({ editor }: Editor) {
	const ai = useTldrawAi({ editor, ...MY_STATIC_OPTIONS })
	return (
		<div style={{ position: 'fixed', bottom: 100, left: 16 }}>
			<button onClick={() => ai.prompt('draw a unicorn')}>Unicorn</button>
		</div>
	)
}
```

## License

This project is provided under the MIT license found [here](https://github.com/tldraw/ai/blob/main/LICENSE.md). The tldraw SDK is provided under the [tldraw license](https://github.com/tldraw/tldraw/blob/main/LICENSE.md).

## Trademarks

Copyright (c) 2024-present tldraw Inc. The tldraw name and logo are trademarks of tldraw. Please see our [trademark guidelines](https://github.com/tldraw/tldraw/blob/main/TRADEMARKS.md) for info on acceptable usage.

## Distributions

You can find the @tldraw/ai package on npm [here](https://www.npmjs.com/package/@tldraw/ai?activeTab=versions). You can find tldraw on npm [here](https://www.npmjs.com/package/@tldraw/tldraw?activeTab=versions).

## Contribution

Found a bug? Please [submit an issue](https://github.com/tldraw/ai/issues/new).

## Community

Have questions, comments or feedback? [Join our discord](https://discord.gg/rhsyWMUJxd). For the latest news and release notes, visit [tldraw.dev](https://tldraw.dev).

## Contact

Find us on Twitter/X at [@tldraw](https://twitter.com/tldraw) or email us at [mailto:<EMAIL>](<EMAIL>).